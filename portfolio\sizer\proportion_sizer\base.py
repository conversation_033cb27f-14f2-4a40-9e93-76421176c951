class BaseProportionSizer:
    def __init__(self, buy_prop=0.1, sell_prop=0.1, min_size=100, round_lot=100):
        self.buy_prop = buy_prop
        self.sell_prop = sell_prop
        self.min_size = min_size
        self.round_lot = round_lot

    def calc_buy_size(self, cash, price, max_position_value=None):
        """
        计算买入数量。优先按标的最大持仓值（如有），否则用可用现金。
        :param cash: 可用现金
        :param price: 当前价格
        :param max_position_value: 标的最大允许持仓金额（可选）
        """
        # 按最大持仓值和现金的较小者
        if max_position_value is not None:
            buy_value = min(cash, max_position_value * self.buy_prop)
        else:
            buy_value = cash * self.buy_prop
        # print(f"计算买入大小: buy_value={buy_value}, buy_prop={self.buy_prop}, cash={cash}, price={price}, max_position_value={max_position_value}")
        if buy_value <= 0 or price <= 0:
            return 0
        size = int(buy_value // price)
        size = (size // self.round_lot) * self.round_lot
        if size < self.min_size:
            return 0
        return size

    def calc_sell_size(self, position_size):
        size = int(position_size * self.sell_prop)
        size = (size // self.round_lot) * self.round_lot
        # print(f"计算卖出大小: position_size={position_size}, sell_prop={self.sell_prop}, size={size}")
        if size < self.min_size:
            return 0
        return size
