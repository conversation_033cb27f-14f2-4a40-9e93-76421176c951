"""
单因子研究框架

基于alphalens构建的单因子研究框架，提供完整的因子分析工具链。

主要功能:
- 因子计算和管理
- 基于alphalens的因子分析
- 丰富的可视化功能
- 与现有数据库系统集成
- 为多因子研究预留扩展接口

使用示例:
    from factor_research import FactorResearch
    
    # 初始化研究框架
    research = FactorResearch(db_manage=db_manage)
    
    # 计算因子
    factor_data = research.calculate_factor(
        factor_name="momentum_20d",
        assets=["000001", "000002"],
        start_date="2023-01-01",
        end_date="2023-12-31"
    )
    
    # 分析因子
    results = research.analyze_factor(factor_data)
    
    # 生成报告
    research.generate_report(results)
"""

from .core.factor_research import FactorResearch
from .core.data_adapter import DataAdapter
from .core.factor_engine import FactorEngine
from .core.analyzer import FactorAnalyzer
from .core.visualizer import FactorVisualizer

from .factors.base import BaseFactor
from .utils.config import FactorConfig

__version__ = "0.1.0"
__author__ = "T_Trade Team"

__all__ = [
    "FactorResearch",
    "DataAdapter", 
    "FactorEngine",
    "FactorAnalyzer",
    "FactorVisualizer",
    "BaseFactor",
    "FactorConfig"
]
