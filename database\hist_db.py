from .quant_db import QuantDB
import pandas as pd
from datetime import datetime, timedelta
import sqlite3

class HistDB:
    def __init__(self, data_engine=None):
        self.db = QuantDB()
        self.data_engine = data_engine  # 可选：用于根据code判断category

    def _get_today_str(self):
        """
        获取今天的日期字符串，如果是周末则返回本周最后一个交易日（周五）。
        """
        today = datetime.now()
        # weekday: Monday=0, ..., Sunday=6
        if today.weekday() >= 5:  # 5=Saturday, 6=Sunday
            # 回退到本周五
            days_back = today.weekday() - 4
            last_trading_day = today - timedelta(days=days_back)
            return last_trading_day.strftime("%Y-%m-%d")
        else:
            return today.strftime("%Y-%m-%d")

    def _fill_missing_columns(self, table, df):
        """
        补齐DataFrame缺失的列，默认填0
        """
        # 获取表结构
        conn = self.db.get_conn() if hasattr(self.db, "get_conn") else self.db.conn
        cursor = conn.execute(f"PRAGMA table_info({table})")
        columns = [row[1] for row in cursor.fetchall()]
        for col in columns:
            if col not in df.columns:
                df[col] = 0
        # 保证列顺序一致
        df = df[columns]
        return df

    def update_hist_data(self, equity_pool, adjust="前复权"):
        """
        根据股票池列表，下载并保存日线、周线、月线数据到对应表格。
        ETF标的保存到etf表，股票标的保存到stock表。
        """
        periods = ["daily", "weekly", "monthly"]
        today_str = self._get_today_str()
        for code_item in equity_pool:
            code = code_item.get("code") if isinstance(code_item, dict) else str(code_item)
            # 判断ETF或股票
            if hasattr(self.data_engine, "is_etf_code"):
                is_etf = self.data_engine.is_etf_code(code)
            else:
                # 兜底：简单判断
                is_etf = code.startswith(("5", "1", "15", "16", "51"))
            category = "etf" if is_etf else "stock"
            for freq in periods:
                table = f"{category}_{freq}"
                try:
                    conn = self.db.get_conn() if hasattr(self.db, "get_conn") else self.db.conn
                    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                    table_exists = cursor.fetchone() is not None
                    need_update = True
                    if table_exists:
                        df_old = self.db.read_table(table, where=f"code='{code}'")
                        if df_old is not None and not df_old.empty:
                            last_row = df_old.sort_values(by="date").iloc[-1]
                            last_date = pd.to_datetime(last_row["date"]).strftime("%Y-%m-%d")
                            if last_date == today_str:
                                need_update = False
                    if not need_update:
                        continue
                    df_new = self.data_engine._get_stock_hist_info(code, freq, adjust)
                    if df_new is not None and not df_new.empty:
                        if "code" not in df_new.columns:
                            df_new["code"] = code
                        if table_exists:
                            df_new = self._fill_missing_columns(table, df_new)
                            if df_old is not None and not df_old.empty:
                                max_date = pd.to_datetime(df_old['date'], format='mixed').max()
                                df_new['date'] = pd.to_datetime(df_new['date'], format='mixed')
                                df_append = df_new[df_new['date'] > max_date]
                                if not df_append.empty:
                                    self.db.insert_df(table, df_append)
                                    print(f"已增量更新{code} {freq} 历史数据, 新增{len(df_append)}条")
                            else:
                                self.db.insert_df(table, df_new)
                        else:
                            self.db.save_data(category, freq, df_new, if_exists="replace")
                except Exception as e:
                    print(f"下载或保存{code} {freq}历史数据失败: {e}")

    def query_hist_data(self, category, freq, code=None, start_date=None, end_date=None):
        """
        查询历史数据表，支持按code筛选和日期范围。
        :param category: 'stock'/'etf'等
        :param freq: 'daily'/'weekly'/'monthly'
        :param code: 可选，若指定则只查该code
        :param start_date: 可选，开始日期
        :param end_date: 可选，结束日期
        """
        if code is not None:
            category = self.data_engine.get_category_by_code(code)
        table = f"{category.lower()}_{freq.lower()}"
        
        conditions = []
        if code:
            conditions.append(f"code='{code}'")
        if start_date:
            conditions.append(f"date >= '{start_date}'")
        if end_date:
            conditions.append(f"date <= '{end_date}'")
        
        where = " AND ".join(conditions) if conditions else None
        return self.db.read_table(table, where=where)

    def update_indicator_data(self, indicator_map, period="daily"):
        """
        批量计算并更新数据库中所有标的的指标
        :param indicator_map: {symbol: indicator_instance}
        """
        for symbol, indicator in indicator_map.items():
            # 统一用self.data_engine判断category
            category = "etf" if self.data_engine.is_etf_code(symbol) else "stock"
            df = self.query_hist_data(category, period, symbol)
            if df is not None and not df.empty:
                # 获取每个indicator实例的params（如有自定义指标参数可通过实例属性传递）
                indicators = getattr(indicator, "params", None)
                if indicators is None and hasattr(indicator, "params"):
                    indicators = indicator.params.get("indicators", None)
                # 若params为dict且有"indicators"键，则取其值
                if isinstance(indicators, dict) and "indicators" in indicators:
                    indicators = indicators["indicators"]
                df = indicator.add_indicators(df, indicators)
                # 直接覆盖写回
                self.db.save_data(category, period, df, if_exists="replace")

    def get_hist_bar_map(self, n=None, symbol_category_map=None):
        """
        获取历史bar数据迭代器，支持多标的，逐交易日推进。
        :param n: 返回前n个交易日（None则返回全部）
        :param symbol_category_map: {symbol: "stock"/"etf"}
        :return: 迭代器，每次yield {"date": date, "latest_data": {symbol: bar_row}}
        """
        if symbol_category_map is None:
            symbol_category_map = {}
        # 合并所有标的的日线数据
        all_dfs = []
        for symbol, category in symbol_category_map.items():
            df = self.query_hist_data(category, "daily", symbol)
            if df is not None and not df.empty:
                df = df.copy()
                df['code'] = symbol  # 保证有code列
                all_dfs.append(df)
        if not all_dfs:
            return
        df = pd.concat(all_dfs, ignore_index=True)
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df = df.sort_values('date')
        symbols = df['code'].unique()
        trade_dates = sorted(df['date'].unique())
        if n is not None:
            trade_dates = trade_dates[:n]
        for date in trade_dates:
            latest_data = {}
            for symbol in symbols:
                row = df[(df['date'] == date) & (df['code'] == symbol)]
                if not row.empty:
                    latest_data[symbol] = row.iloc[-1].to_dict()
            yield {"date": date, "latest_data": latest_data}


