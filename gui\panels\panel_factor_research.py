"""
因子研究面板

提供图形界面进行因子研究，包括：
- 因子选择和参数配置
- 股票池选择
- 时间范围设置
- 因子计算和分析
- 结果可视化
- 报告生成
"""

import sys
import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QTabWidget,
    QPushButton, QLabel, QLineEdit, QComboBox, QListWidget, QTextEdit,
    QDateEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox,
    QProgressBar, QMessageBox, QFileDialog, QTableWidget, QTableWidgetItem,
    QSplitter, QFrame, QScrollArea
)
from PyQt5.QtCore import QDate, QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPixmap
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 导入因子研究框架
try:
    from factor_research import FactorResearch
    from factor_research.factors.momentum import MomentumFactor, RSIFactor, PriceVolumeTrendFactor
    FACTOR_RESEARCH_AVAILABLE = True
except ImportError as e:
    FACTOR_RESEARCH_AVAILABLE = False
    logging.warning(f"Factor research framework not available: {e}")

logger = logging.getLogger(__name__)


class FactorCalculationThread(QThread):
    """因子计算线程"""
    progress_updated = pyqtSignal(int)
    calculation_finished = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, research, factor_name, assets, start_date, end_date, params):
        super().__init__()
        self.research = research
        self.factor_name = factor_name
        self.assets = assets
        self.start_date = start_date
        self.end_date = end_date
        self.params = params
        
    def run(self):
        try:
            self.progress_updated.emit(10)
            
            # 计算因子
            factor_data = self.research.calculate_factor(
                factor_name=self.factor_name,
                assets=self.assets,
                start_date=self.start_date,
                end_date=self.end_date,
                **self.params
            )
            self.progress_updated.emit(50)
            
            # 分析因子
            results = self.research.analyze_factor(factor_data)
            self.progress_updated.emit(90)
            
            self.progress_updated.emit(100)
            self.calculation_finished.emit({
                'factor_data': factor_data,
                'analysis_results': results,
                'factor_name': self.factor_name
            })
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class PanelFactorResearch(QWidget):
    """因子研究面板"""
    
    def __init__(self, parent=None, db_manage=None):
        super().__init__(parent)
        self.db_manage = db_manage
        self.research = None
        self.current_results = None
        
        # 初始化因子研究框架
        self._init_factor_research()
        
        # 初始化UI
        self._init_ui()
        
        # 初始化数据
        self._init_data()
        
    def _init_factor_research(self):
        """初始化因子研究框架"""
        if not FACTOR_RESEARCH_AVAILABLE:
            return
            
        try:
            self.research = FactorResearch(db_manage=self.db_manage)
            
            # 注册默认因子
            self.research.register_factor(MomentumFactor, lookback_period=20)
            self.research.register_factor(RSIFactor, period=14)
            self.research.register_factor(PriceVolumeTrendFactor, period=20)
            
            logger.info("Factor research framework initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize factor research framework: {e}")
            self.research = None
    
    def _init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        main_layout.setSpacing(3)  # 减少组件间距
        
        # 标题 - 适中布局
        title_label = QLabel("📈 因子研究")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))  # 增加字体大小
        title_label.setMaximumHeight(35)  # 增加最大高度
        title_label.setMinimumHeight(35)  # 增加最小高度
        title_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        title_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 6px 12px;  /* 增加内边距 */
                margin: 3px 0px;
                font-size: 12px;    /* 确保字体大小 */
                line-height: 1.2;   /* 设置行高 */
            }
        """)
        main_layout.addWidget(title_label)
        
        if not FACTOR_RESEARCH_AVAILABLE:
            # 显示错误信息
            error_label = QLabel("因子研究框架不可用，请检查安装")
            error_label.setStyleSheet("color: red; font-size: 14px;")
            main_layout.addWidget(error_label)
            self.setLayout(main_layout)
            return
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧控制面板
        control_panel = self._create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧结果面板
        result_panel = self._create_result_panel()
        splitter.addWidget(result_panel)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
    
    def _create_control_panel(self):
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # 因子选择组 - 均衡布局
        factor_group = QGroupBox("📈 因子配置")
        factor_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;  /* 为标题留出空间 */
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
                color: #333333;
            }
        """)
        factor_layout = QVBoxLayout()
        factor_layout.setSpacing(8)  # 增加组件间距
        factor_layout.setContentsMargins(12, 15, 12, 10)  # 增加顶部边距为标题留空间

        # 因子类型选择 - 水平布局
        factor_type_layout = QHBoxLayout()
        factor_label = QLabel("类型:")
        factor_label.setFixedWidth(50)  # 稍微增加标签宽度
        factor_label.setStyleSheet("font-weight: bold;")

        self.factor_combo = QComboBox()
        # 使用更简洁的因子名称
        self.factor_combo.addItems(["动量因子", "RSI因子", "价量趋势"])
        self.factor_combo.setMinimumHeight(35)  # 增加最小高度
        self.factor_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        factor_type_layout.addWidget(factor_label)
        factor_type_layout.addWidget(self.factor_combo)
        factor_type_layout.addStretch()  # 添加弹性空间

        factor_layout.addLayout(factor_type_layout)

        # 因子参数区域
        self.param_layout = QVBoxLayout()
        self.param_layout.setContentsMargins(0, 8, 0, 5)  # 调整参数区域边距
        self.param_layout.setSpacing(5)
        self._update_factor_params()
        factor_layout.addLayout(self.param_layout)

        self.factor_combo.currentTextChanged.connect(self._update_factor_params)
        factor_group.setLayout(factor_layout)
        factor_group.setMinimumHeight(100)  # 设置最小高度而非最大高度
        factor_group.setMaximumHeight(150)  # 适当放宽最大高度限制
        layout.addWidget(factor_group)
        
        # 股票池选择组 - 均衡布局
        stock_group = QGroupBox("🏢 股票池")
        stock_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;  /* 为标题留出空间 */
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
                color: #333333;
            }
        """)
        stock_main_layout = QVBoxLayout()
        stock_main_layout.setSpacing(8)
        stock_main_layout.setContentsMargins(12, 15, 12, 10)  # 增加顶部边距为标题留空间

        # 第一行：股票代码输入
        stock_input_layout = QHBoxLayout()
        stock_label = QLabel("股票代码:")
        stock_label.setFixedWidth(80)
        stock_label.setStyleSheet("font-weight: bold;")

        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText("输入股票代码，用逗号分隔，如: 000001,000002,600000")
        self.stock_input.setText("000001,000002,600000")
        self.stock_input.setMinimumHeight(35)
        self.stock_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        stock_input_layout.addWidget(stock_label)
        stock_input_layout.addWidget(self.stock_input)

        # 第二行：快速选择按钮
        stock_button_layout = QHBoxLayout()
        quick_select_btn = QPushButton("📋 快速选择常用股票池")
        quick_select_btn.setMinimumHeight(30)
        quick_select_btn.setToolTip("选择预设的股票池")
        quick_select_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        quick_select_btn.clicked.connect(self._show_quick_select)

        stock_button_layout.addWidget(quick_select_btn)
        stock_button_layout.addStretch()

        stock_main_layout.addLayout(stock_input_layout)
        stock_main_layout.addLayout(stock_button_layout)

        stock_group.setLayout(stock_main_layout)
        stock_group.setMinimumHeight(90)
        stock_group.setMaximumHeight(120)
        layout.addWidget(stock_group)
        
        # 时间范围组 - 均衡布局
        time_group = QGroupBox("📅 时间范围")
        time_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;  /* 为标题留出空间 */
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
                color: #333333;
            }
        """)
        time_main_layout = QVBoxLayout()
        time_main_layout.setSpacing(8)
        time_main_layout.setContentsMargins(12, 15, 12, 10)  # 增加顶部边距为标题留空间

        # 第一行：日期选择
        time_layout = QHBoxLayout()
        time_layout.setSpacing(15)

        # 开始日期
        start_label = QLabel("开始日期:")
        start_label.setFixedWidth(70)
        start_label.setStyleSheet("font-weight: bold;")

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addYears(-1))
        self.start_date.setCalendarPopup(True)
        self.start_date.setMinimumHeight(35)
        self.start_date.setDisplayFormat("yyyy-MM-dd")
        self.start_date.setStyleSheet("""
            QDateEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        # 结束日期
        end_label = QLabel("结束日期:")
        end_label.setFixedWidth(70)
        end_label.setStyleSheet("font-weight: bold;")

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        self.end_date.setMinimumHeight(35)
        self.end_date.setDisplayFormat("yyyy-MM-dd")
        self.end_date.setStyleSheet("""
            QDateEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        """)

        time_layout.addWidget(start_label)
        time_layout.addWidget(self.start_date)
        time_layout.addWidget(end_label)
        time_layout.addWidget(self.end_date)
        time_layout.addStretch()

        # 第二行：快速时间选择
        quick_time_layout = QHBoxLayout()
        quick_time_label = QLabel("快速选择:")
        quick_time_label.setStyleSheet("font-weight: bold; color: #666;")
        quick_time_layout.addWidget(quick_time_label)

        # 快速时间按钮
        time_buttons = [
            ("近1月", 30),
            ("近3月", 90),
            ("近6月", 180),
            ("近1年", 365)
        ]

        for text, days in time_buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(25)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    padding: 3px 8px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)
            btn.clicked.connect(lambda _, d=days: self._set_quick_time_range(d))
            quick_time_layout.addWidget(btn)

        quick_time_layout.addStretch()

        time_main_layout.addLayout(time_layout)
        time_main_layout.addLayout(quick_time_layout)

        time_group.setLayout(time_main_layout)
        time_group.setMinimumHeight(100)
        time_group.setMaximumHeight(130)
        layout.addWidget(time_group)
        
        # 控制按钮 - 水平布局
        button_layout = QHBoxLayout()

        self.calculate_btn = QPushButton("🔍 计算因子")
        self.calculate_btn.clicked.connect(self._calculate_factor)
        self.calculate_btn.setMinimumHeight(35)
        self.calculate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

        self.export_btn = QPushButton("📊 导出报告")
        self.export_btn.clicked.connect(self._export_report)
        self.export_btn.setEnabled(False)
        self.export_btn.setMinimumHeight(35)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)

        button_layout.addWidget(self.calculate_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.setContentsMargins(10, 5, 10, 5)

        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        panel.setLayout(layout)
        return panel
    
    def _create_result_panel(self):
        """创建结果面板"""
        panel = QTabWidget()
        
        # 结果概览标签页
        overview_tab = QWidget()
        overview_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setPlaceholderText("因子分析结果将在这里显示...")
        overview_layout.addWidget(self.result_text)
        
        overview_tab.setLayout(overview_layout)
        panel.addTab(overview_tab, "分析结果")
        
        # 数据表格标签页
        data_tab = QWidget()
        data_layout = QVBoxLayout()
        
        self.data_table = QTableWidget()
        data_layout.addWidget(self.data_table)
        
        data_tab.setLayout(data_layout)
        panel.addTab(data_tab, "因子数据")
        
        # 图表标签页
        chart_tab = QWidget()
        chart_layout = QVBoxLayout()
        
        self.chart_label = QLabel("图表将在这里显示")
        self.chart_label.setAlignment(Qt.AlignCenter)
        self.chart_label.setStyleSheet("border: 1px solid gray; min-height: 300px;")
        chart_layout.addWidget(self.chart_label)
        
        chart_tab.setLayout(chart_layout)
        panel.addTab(chart_tab, "可视化")
        
        return panel

    def _update_factor_params(self):
        """更新因子参数界面"""
        # 清除现有参数控件
        for i in reversed(range(self.param_layout.count())):
            child = self.param_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        factor_name = self.factor_combo.currentText()

        if factor_name == "动量因子":
            # 动量因子参数 - 水平布局
            momentum_layout = QHBoxLayout()

            lookback_label = QLabel("回看:")
            lookback_label.setFixedWidth(40)
            self.lookback_spin = QSpinBox()
            self.lookback_spin.setRange(1, 252)
            self.lookback_spin.setValue(20)
            self.lookback_spin.setMaximumHeight(25)
            self.lookback_spin.setSuffix("天")

            skip_label = QLabel("跳过:")
            skip_label.setFixedWidth(40)
            self.skip_spin = QSpinBox()
            self.skip_spin.setRange(0, 10)
            self.skip_spin.setValue(1)
            self.skip_spin.setMaximumHeight(25)
            self.skip_spin.setSuffix("天")

            momentum_layout.addWidget(lookback_label)
            momentum_layout.addWidget(self.lookback_spin)
            momentum_layout.addWidget(skip_label)
            momentum_layout.addWidget(self.skip_spin)
            momentum_layout.addStretch()

            self.param_layout.addLayout(momentum_layout)

        elif factor_name == "RSI因子":
            # RSI因子参数 - 水平布局
            rsi_layout = QHBoxLayout()

            rsi_label = QLabel("周期:")
            rsi_label.setFixedWidth(40)
            self.rsi_period_spin = QSpinBox()
            self.rsi_period_spin.setRange(2, 100)
            self.rsi_period_spin.setValue(14)
            self.rsi_period_spin.setMaximumHeight(25)
            self.rsi_period_spin.setSuffix("天")

            rsi_layout.addWidget(rsi_label)
            rsi_layout.addWidget(self.rsi_period_spin)
            rsi_layout.addStretch()

            self.param_layout.addLayout(rsi_layout)

        elif factor_name == "价量趋势":
            # 价量趋势因子参数 - 水平布局
            pvt_layout = QHBoxLayout()

            pvt_label = QLabel("周期:")
            pvt_label.setFixedWidth(40)
            self.pvt_period_spin = QSpinBox()
            self.pvt_period_spin.setRange(1, 100)
            self.pvt_period_spin.setValue(20)
            self.pvt_period_spin.setMaximumHeight(25)
            self.pvt_period_spin.setSuffix("天")

            pvt_layout.addWidget(pvt_label)
            pvt_layout.addWidget(self.pvt_period_spin)
            pvt_layout.addStretch()

            self.param_layout.addLayout(pvt_layout)

    def _init_data(self):
        """初始化数据"""
        pass

    def _get_factor_class_name(self, display_name):
        """将显示名称映射为实际的因子类名"""
        factor_mapping = {
            "动量因子": "MomentumFactor",
            "RSI因子": "RSIFactor",
            "价量趋势": "PriceVolumeTrendFactor"
        }
        return factor_mapping.get(display_name, display_name)

    def _calculate_factor(self):
        """计算因子"""
        if not self.research:
            QMessageBox.warning(self, "错误", "因子研究框架未初始化")
            return

        # 获取参数
        factor_display_name = self.factor_combo.currentText()
        factor_name = self._get_factor_class_name(factor_display_name)  # 转换为实际类名

        assets_text = self.stock_input.text().strip()
        if not assets_text:
            QMessageBox.warning(self, "错误", "请输入股票代码")
            return

        assets = [code.strip() for code in assets_text.split(',') if code.strip()]
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")

        # 获取因子参数
        params = self._get_factor_params()

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.calculate_btn.setEnabled(False)

        # 启动计算线程
        self.calc_thread = FactorCalculationThread(
            self.research, factor_name, assets, start_date, end_date, params
        )
        self.calc_thread.progress_updated.connect(self.progress_bar.setValue)
        self.calc_thread.calculation_finished.connect(self._on_calculation_finished)
        self.calc_thread.error_occurred.connect(self._on_calculation_error)
        self.calc_thread.start()

    def _get_factor_params(self):
        """获取因子参数"""
        factor_name = self.factor_combo.currentText()
        params = {}

        if factor_name == "动量因子":
            params['lookback_period'] = self.lookback_spin.value()
            params['skip_period'] = self.skip_spin.value()
        elif factor_name == "RSI因子":
            params['period'] = self.rsi_period_spin.value()
        elif factor_name == "价量趋势":
            params['period'] = self.pvt_period_spin.value()

        return params

    def _on_calculation_finished(self, results):
        """计算完成回调"""
        self.current_results = results
        self.progress_bar.setVisible(False)
        self.calculate_btn.setEnabled(True)
        self.export_btn.setEnabled(True)

        # 显示结果
        self._display_results(results)

        QMessageBox.information(self, "完成", "因子计算和分析完成！")

    def _on_calculation_error(self, error_msg):
        """计算错误回调"""
        self.progress_bar.setVisible(False)
        self.calculate_btn.setEnabled(True)

        QMessageBox.critical(self, "计算错误", f"因子计算失败：\n{error_msg}")

    def _display_results(self, results):
        """显示分析结果"""
        factor_data = results['factor_data']
        analysis_results = results['analysis_results']
        factor_name = results['factor_name']

        # 显示文本结果
        result_text = f"因子: {factor_name}\n"
        result_text += f"数据点数: {len(factor_data)}\n"
        result_text += f"时间范围: {factor_data.index.get_level_values(0).min()} 到 {factor_data.index.get_level_values(0).max()}\n"
        result_text += f"股票数量: {len(factor_data.index.get_level_values(1).unique())}\n\n"

        # 添加统计信息
        result_text += "因子统计:\n"
        result_text += f"均值: {factor_data.mean():.4f}\n"
        result_text += f"标准差: {factor_data.std():.4f}\n"
        result_text += f"最小值: {factor_data.min():.4f}\n"
        result_text += f"最大值: {factor_data.max():.4f}\n\n"

        # 添加分析结果
        if analysis_results and 'ic_summary' in analysis_results:
            ic_summary = analysis_results['ic_summary']
            result_text += "IC分析:\n"
            result_text += f"IC均值: {ic_summary.get('IC Mean', 'N/A')}\n"
            result_text += f"IC标准差: {ic_summary.get('IC Std', 'N/A')}\n"
            result_text += f"信息比率: {ic_summary.get('IR', 'N/A')}\n"

        self.result_text.setText(result_text)

        # 显示数据表格
        self._display_data_table(factor_data)

    def _display_data_table(self, factor_data):
        """显示因子数据表格"""
        # 转换为DataFrame用于显示
        df = factor_data.reset_index()
        df.columns = ['日期', '股票代码', '因子值']

        # 设置表格
        self.data_table.setRowCount(min(len(df), 1000))  # 最多显示1000行
        self.data_table.setColumnCount(3)
        self.data_table.setHorizontalHeaderLabels(['日期', '股票代码', '因子值'])

        # 填充数据
        for i in range(min(len(df), 1000)):
            self.data_table.setItem(i, 0, QTableWidgetItem(str(df.iloc[i, 0])))
            self.data_table.setItem(i, 1, QTableWidgetItem(str(df.iloc[i, 1])))
            self.data_table.setItem(i, 2, QTableWidgetItem(f"{df.iloc[i, 2]:.4f}"))

        # 调整列宽
        self.data_table.resizeColumnsToContents()

    def _export_report(self):
        """导出分析报告"""
        if not self.current_results:
            QMessageBox.warning(self, "错误", "没有可导出的结果")
            return

        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存报告", f"factor_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            "HTML文件 (*.html)"
        )

        if file_path:
            try:
                # 生成HTML报告
                self.research.generate_report(
                    self.current_results['analysis_results'],
                    file_path
                )
                QMessageBox.information(self, "成功", f"报告已保存到：\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出报告失败：\n{str(e)}")

    def _show_quick_select(self):
        """显示快速选择股票池对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QPushButton

        dialog = QDialog(self)
        dialog.setWindowTitle("快速选择股票池")
        dialog.setFixedSize(300, 250)

        layout = QVBoxLayout()

        # 预设股票池
        stock_pools = {
            "大盘蓝筹": "000001,000002,600000,600036,600519",
            "科技股": "000858,002415,300059,300750,688981",
            "银行股": "000001,600000,600036,601318,601398",
            "消费股": "000858,600519,000568,002304,603288",
            "测试池": "000001,000002,600000"
        }

        # 添加股票池按钮
        for name, codes in stock_pools.items():
            btn = QPushButton(f"{name} ({len(codes.split(','))}只)")
            btn.setToolTip(f"股票代码: {codes}")
            btn.clicked.connect(lambda _, c=codes: self._select_stock_pool(c, dialog))
            layout.addWidget(btn)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)
        layout.addWidget(cancel_btn)

        dialog.setLayout(layout)
        dialog.exec_()

    def _select_stock_pool(self, codes, dialog):
        """选择股票池"""
        self.stock_input.setText(codes)
        dialog.accept()

    def _set_quick_time_range(self, days):
        """设置快速时间范围"""
        from PyQt5.QtCore import QDate

        end_date = QDate.currentDate()
        start_date = end_date.addDays(-days)

        self.start_date.setDate(start_date)
        self.end_date.setDate(end_date)
