class TradeBroker:
    def __init__(self):
        # 总资金
        self.cash = 0
        # 标的持仓: {symbol: {'quantity': int, 'avg_price': float}}
        self.positions = {}
        self.price_map = {}
        # 如果传入account_info则初始化资金和持仓

    def load_account_info(self, account_info):
        """
        从账户信息加载资金和持仓到broker
        """
        self.cash = account_info.get("cash", 0)
        self.positions = account_info.get("positions", {}).copy()

    def reset(self, initial_cash: float):
        """重新设置初始资金"""
        self.cash = initial_cash
        self.positions.clear()
        self.price_map.clear()

    def update_price_map(self, price_map: dict):
        """动态更新最新行情数据"""
        self.price_map = price_map.copy()

    def get_cash(self):
        return self.cash

    def get_position(self, symbol):
        """
        查询单个标的持仓信息，返回 {'quantity': int, 'avg_price': float}
        持仓中通常不需要存储最新价，最新价应通过price_map或行情获取。
        """
        return self.positions.get(symbol, {'quantity': 0, 'avg_price': 0.0})

    def get_price(self, symbol):
        """
        获取指定标的的最新价格，优先用self.price_map，没有则用持仓均价
        如果price_map和持仓均价都没有，抛出异常
        """
        if symbol in self.price_map:
            return self.price_map[symbol]
        pos = self.get_position(symbol)
        price = pos.get("avg_price", 0.0)
        if price == 0.0:
            raise ValueError(f"无法获取{symbol}的最新价格（price_map和持仓均价均无数据）")
        return price

    def _total_market_value(self, price_map: dict = None):
        """
        计算所有持仓的总市值。
        允许传入price_map参数，是为了支持在某些场景下（如临时估值、回测、批量模拟等）用不同于当前broker的最新价格进行市值计算。
        如果不传，则默认用self.price_map。
        这样更灵活，既能用broker当前行情，也能支持外部自定义行情。
        """
        pm = price_map or self.price_map
        total = 0.0
        for symbol, pos in self.positions.items():
            price = pm.get(symbol, pos['avg_price'])
            total += pos['quantity'] * price
        return total

    def buy(self, symbol: str, price: float, quantity: int, price_map: dict = None, date=None):
        cost = price * quantity
        if cost > self.cash:
            raise ValueError("Insufficient cash to buy")
        # 持仓比例限制已转移到risk_manage，这里不再判断
        self.cash -= cost
        pos = self.positions.get(symbol, {'quantity': 0, 'avg_price': 0.0})
        total_quantity = pos['quantity'] + quantity
        if total_quantity == 0:
            avg_price = 0.0
        else:
            avg_price = (pos['avg_price'] * pos['quantity'] + price * quantity) / total_quantity
        self.positions[symbol] = {
            'quantity': total_quantity,
            'avg_price': avg_price
        }
        # 买入时 profit 恒为0
        return {
            "action": "buy",
            "symbol": symbol,
            "price": price,
            "quantity": quantity,
            "cost": cost,
            "profit": 0,  # 买入无盈亏
            "date": date
        }

    def sell(self, symbol: str, price: float, quantity: int, date=None):
        pos = self.positions.get(symbol)
        if not pos or pos['quantity'] < quantity:
            raise ValueError("Insufficient position to sell")
        avg_price = pos['avg_price']
        proceeds = price * quantity
        cost = avg_price * quantity
        profit = proceeds - cost  # 不含手续费/税费，如需可自行扩展
        self.cash += proceeds
        pos['quantity'] -= quantity
        if pos['quantity'] == 0:
            del self.positions[symbol]
        else:
            self.positions[symbol] = pos
        return {
            "action": "sell",
            "symbol": symbol,
            "price": price,
            "quantity": quantity,
            "proceeds": proceeds,
            "cost": cost,
            "profit": profit,
            "date": date
        }

    def get_all_positions(self):
        return self.positions.copy()

    def get_value(self, price_map: dict = None):
        """
        获取账户总市值 = 现金 + 所有持仓市值
        price_map: {symbol: price}，如未提供则用self.price_map，再否则用持仓均价
        """
        pm = price_map or self.price_map
        total = self.cash
        # print(f"当前现金: {self.cash}, 持仓市值: {self._total_market_value(pm)}")
        for symbol, pos in self.positions.items():
            price = pm.get(symbol, pos['avg_price'])
            total += pos['quantity'] * price
        return total
