from strategies.trade_strategy.unified_strategy_base import UnifiedStrategyBase
from strategies.trade_strategy.gold_bollinger_strategy.signal import StrategySignal

class TradeGoldBollingerStrategy(UnifiedStrategyBase):
    def __init__(self, name, sizer, indicator, position_manager, broker, params=None):
        super().__init__(name, sizer, indicator, position_manager, broker, params)
        # 适配参数格式 {'indicators': {'bollinger': {...}, 'vol_ma': {...}}}
        indicators = (params or {}).get("indicators", {})
        boll_params = indicators.get("bollinger", {})
        vol_ma_params = indicators.get("vol_ma", {})
        self.signal = StrategySignal(
            period=boll_params.get('period', 15),
            dev=boll_params.get('dev', 2),
            vol_ma_period=vol_ma_params.get('period', 5)
        )

    def generate_signal(self, data, debug=False):
        signal = self.signal.get_signal(data, debug=debug)
        debug_info = {"signal": signal}
        return signal, debug_info

    def execute_order(self, signal, size, data, debug=False):
        # 调用broker执行下单逻辑
        symbol = data.get("symbol") or data.get("code")
        price = data.get("close")
        date = data.get("date")
        trade_info = {}
        if signal > 0 and size > 0:
            # 买入
            trade_info = self.broker.buy(symbol, price, size, date=date)
        elif signal < 0 and size > 0:
            # 卖出
            trade_info = self.broker.sell(symbol, price, size, date=date)

        return trade_info
