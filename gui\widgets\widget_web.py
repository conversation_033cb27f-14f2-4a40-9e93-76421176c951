from PyQt5.QtWidgets import QWidget, QVBoxLayout
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage
from PyQt5.QtCore import QUrl
import os

class CustomWebPage(QWebEnginePage):
    """
    Custom QWebEnginePage to inject JavaScript polyfills for compatibility.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.loadFinished.connect(self._on_load_finished)

    def _on_load_finished(self, ok):
        """
        Injects polyfills after the page has finished loading.
        """
        if ok:
            polyfill_script = """
            // Polyfill for String.prototype.replaceAll
            if (!String.prototype.replaceAll) {
                String.prototype.replaceAll = function(search, replacement) {
                    var target = this;
                    return target.split(search).join(replacement);
                };
            }
            // Polyfill for Array.prototype.at
            if (!Array.prototype.at) {
                Array.prototype.at = function(n) {
                    n = Math.trunc(n) || 0;
                    if (n < 0) n += this.length;
                    if (n < 0 || n >= this.length) return undefined;
                    return this[n];
                };
            }
            // Polyfill for String.prototype.at
            if (!String.prototype.at) {
                String.prototype.at = function(n) {
                    n = Math.trunc(n) || 0;
                    if (n < 0) n += this.length;
                    if (n < 0 || n >= this.length) return undefined;
                    return this.charAt(n);
                };
            }
            """
            self.runJavaScript(polyfill_script)

class WebPanel(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._web_view = QWebEngineView()
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self._web_view)
        self.setLayout(layout)
        self._web_view.setPage(CustomWebPage(self))

    def show_file(self, file_path):
        if os.path.exists(file_path):
            self._web_view.setUrl(QUrl.fromLocalFile(os.path.abspath(file_path)))
        else:
            self.set_html(f"<html><body><p>File not found: {file_path}</p></body></html>")

    def set_html(self, html_content):
        self._web_view.setHtml(html_content)
