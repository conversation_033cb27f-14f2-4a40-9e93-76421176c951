import os
import json
import datetime
import backtrader as bt
import pandas as pd
import akshare as ak

from data_gen.data_generator_em import DataGeneratorEM
# Explicit imports to fix the ImportError
from backtest.strategies.bollinger_strategy import BollingerStrategy
from backtest.analyzer.calmar_ratio_analyzer import CalmarRatioAnalyzer
from backtest.analyzer.sharp_ratio_analyzer import SharpeRatioAnalyzer
from backtest.evaluator.rolling_evaluator import RollingEvaluator
from backtest.evaluator.simple_evaluator import SimpleEvaluator
from backtest.optimizer.optuna import OptunaOptimizer

class GoldAnalyze(DataGeneratorEM):
    def __init__(self, date_range ):
        super().__init__(date_range)
        self.df = None
        # self.weekly_params = {'period': 28, 'dev': 2, 'buy_size': 0.1, 'sell_size': 0.4, 'vol_ma_period': 6}

    def _download_data(self, period="daily"):
        etf_path = f"data/market/gold/518880_{period}.csv"
        etf_df = ak.fund_etf_hist_em("518880", period, start_date=self.date_range[0], end_date=self.date_range[1])
        etf_df = self._convert_name_em(etf_df)
        etf_df.to_csv(etf_path, index=False)
        return etf_df
    
    def _load_data(self, period="daily"):
        etf_path = f"data/market/gold/518880_{period}.csv"
        if not os.path.exists(etf_path):
            raw_df = self._download_data(period)
        else:
            raw_df = self._read_csv(etf_path)
            last_date = pd.to_datetime(raw_df['date'].iloc[-1])
            if last_date < pd.to_datetime(self.date_range[1]):
                raw_df = self._download_data(period)

        self.df = raw_df[['date', 'open', 'high', 'low', 'close', 'volume']]
        self.df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        self.df.set_index('datetime', inplace=True)
        self.df['openinterest'] = 0

    def _update_data(self, start_date, period="daily"):
        self._load_data(period)
        if self.df.index[-1] < pd.to_datetime(start_date):
            fund_data = ak.fund_etf_spot_em()
            gold_data = fund_data[fund_data["代码"] == "518880"]

            # Extract real-time data and append to historical data
            if not gold_data.empty:
                real_time_row = {
                    'datetime': pd.to_datetime(gold_data["数据日期"].iloc[0]),
                    'open': gold_data["开盘价"].iloc[0],
                    'high': gold_data["最高价"].iloc[0],
                    'low': gold_data["最低价"].iloc[0],
                    'close': gold_data["最新价"].iloc[0],
                    'volume': gold_data["成交量"].iloc[0],
                    'openinterest': 0
                }
                real_time_df = pd.DataFrame([real_time_row]).set_index('datetime')

                # Check if the last row's date is less than today's date
                self.df = pd.concat([self.df, real_time_df])

    def _param_search(self, period, n_trials=100):
        # Create params/gold directory if it doesn't exist
        params_dir = os.path.join("params", "gold")
        os.makedirs(params_dir, exist_ok=True)

        self._load_data(period)
        evaluator = RollingEvaluator(step_size=21, window_size=252, analyzer_cls=CalmarRatioAnalyzer)
        optimizer = OptunaOptimizer(BollingerStrategy, self.df, evaluator)
        best_params = optimizer._run(n_trials)
        
        # Add timestamp to the parameters
        best_params_with_time = {
            "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "parameters": best_params
        }
        
        # Create filename based on strategy and analyzer names
        strategy_name = BollingerStrategy.__name__
        filename = f"{strategy_name}_params_{period}.json"
        filepath = os.path.join(params_dir, filename)
        
        # Load existing parameters if the file exists
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                try:
                    params_list = json.load(f)
                except json.JSONDecodeError:
                    params_list = []  # Handle invalid JSON by starting fresh
        else:
            params_list = []

        # Append the new parameters to the list
        params_list.append(best_params_with_time)
        
        # Save the updated parameters list to the JSON file
        with open(filepath, 'w') as f:
            json.dump(params_list, f, indent=4)
            
        print(f"Best Parameters: {best_params}")
        print(f"Parameters saved to: {filepath}")
        
        return best_params

    def _gold_monitor(self, strategy=BollingerStrategy, period="daily", plot=False):
        # Load optimal parameters based on strategy and period
        strategy_name = strategy.__name__
        filename = f"{strategy_name}_params_{period}.json"
        filepath = os.path.join("params", "gold", filename)

        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                params = json.load(f)[-1]['parameters']
            print(f"Loaded parameters from {filepath}")
        else:
            print('Error: Parameter file not found. Please run _param_search() first.')
            return

        self._update_data(self.date_range[0], period)
        evaluator = SimpleEvaluator(plot, analyzer_cls=SharpeRatioAnalyzer)
        (sharp_ratio, last_signal) = evaluator.evaluate(strategy, self.df, params)
        print(f"Sharp Ratio: {sharp_ratio}")
        last_signal['code'] = '518880'
        return last_signal