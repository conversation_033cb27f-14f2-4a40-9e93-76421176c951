import pandas as pd

class UnifiedStrategyBase:
    """
    统一策略基类，封装完整交易流水线，on_bar为统一入口。
    子类只需实现 generate_signal 和下单方法。
    """

    def __init__(self, name, sizer, indicator, position_manager, broker, params=None):
        self.name = name
        self.sizer = sizer
        self.indicator = indicator
        self.position_manager = position_manager
        self.broker = broker
        self.params = params or {}
        self.last_trade_price = None
        self.trade_records = []
        self.last_signal_date = None
        self.last_signal_type = None
        self._hist_df = pd.DataFrame()  # 初始化为空DataFrame，便于后续直接拼接和计算

    def on_bar(self, data, debug=False):
        # 1. 指标计算
        indicator_params = self.params.get("indicators", {}) if hasattr(self, "params") else {}
        # 获取历史数据缓存（如有）
        indicators = self.indicator.calculate(self._hist_df, params=indicator_params)
        # 将指标写入bar数据，便于信号函数直接用
        if indicators and isinstance(data, dict):
            data.update(indicators)

        # 2. 信号生成
        symbol = data.get("code")
        signal, signal_info = self.generate_signal(data, debug)

        # 新增：信号去重逻辑
        bar_date = data.get("date")
        if bar_date is not None:
            bar_date = str(bar_date)[:10]  # 只取日期部分
        if (
            bar_date is not None
            and signal is not None
            and signal == self.last_signal_type
            and bar_date == self.last_signal_date
        ):
            # 已在本周期触发过同类信号，忽略
            return None
        # 3. 风控检查
        position = self.position_manager.get_position(symbol)
        allow_trade = self.position_manager.check_risk(
            symbol=symbol,
            signal=signal,
            position=position,
            data=data,
            broker=self.broker
        )
        # 4. 仓位计算
        size = self.sizer.get_size(symbol, signal, position, self.broker) if allow_trade else 0
        # 5. 订单执行（可由子类实现具体 buy/sell）
        trade_info = self.execute_order(signal, size, data, debug=debug)
        if debug and signal_info:
            trade_info.update(signal_info)
        if trade_info:
            print(f"Trade executed: {trade_info}")
            self.trade_records.append(trade_info)
        # print(f"trade records: {self.trade_records[-1]}")
        self.last_signal_date = bar_date
        self.last_signal_type = signal
        return trade_info

    def generate_signal(self, data, debug=False):
        """
        子类实现：根据数据和指标生成买卖信号
        """
        raise NotImplementedError

    def execute_order(self, signal, size, data, debug=False):
        """
        子类实现：根据信号和仓位执行下单
        """
        raise NotImplementedError
