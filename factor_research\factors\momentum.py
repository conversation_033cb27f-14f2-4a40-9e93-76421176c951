"""
动量因子

实现各种动量类因子，包括价格动量、收益率动量等。
"""

from typing import List, Dict, Any
import pandas as pd
import numpy as np
import logging

from .base import BaseFactor

logger = logging.getLogger(__name__)


class MomentumFactor(BaseFactor):
    """
    动量因子
    
    计算指定期间的价格动量，支持多种计算方式。
    """
    
    def __init__(self, lookback_period: int = 20, skip_period: int = 1,
                 method: str = 'return', **kwargs):
        """
        初始化动量因子

        Parameters:
        -----------
        lookback_period : int, optional
            回看期，默认20天
        skip_period : int, optional
            跳过期，默认1天（跳过最近1天避免反转效应）
        method : str, optional
            计算方法，'return'(收益率) 或 'log_return'(对数收益率)，默认'return'
        **kwargs : dict
            其他参数
        """
        # 确保传递name参数
        if 'name' not in kwargs:
            kwargs['name'] = 'MomentumFactor'
        if 'description' not in kwargs:
            kwargs['description'] = f'Momentum factor with {lookback_period}D lookback and {skip_period}D skip'
        if 'category' not in kwargs:
            kwargs['category'] = 'technical'

        super().__init__(**kwargs)
        self.lookback_period = lookback_period
        self.skip_period = skip_period
        self.method = method
        
        # 验证参数
        if lookback_period <= 0:
            raise ValueError("lookback_period must be positive")
        if skip_period < 0:
            raise ValueError("skip_period must be non-negative")
        if method not in ['return', 'log_return']:
            raise ValueError("method must be 'return' or 'log_return'")
            
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算动量因子
        
        Parameters:
        -----------
        data : pd.DataFrame
            价格数据，MultiIndex (date, asset)，包含 'close' 列
        **params : dict
            运行时参数，可覆盖初始化参数
            
        Returns:
        --------
        pd.Series
            动量因子值，MultiIndex (date, asset)
        """
        try:
            # 获取参数
            lookback = params.get('lookback_period', self.lookback_period)
            skip = params.get('skip_period', self.skip_period)
            method = params.get('method', self.method)
            
            # 验证数据
            self.validate_data(data)
            
            if 'close' not in data.columns:
                raise ValueError("Data must contain 'close' column")
                
            # 获取收盘价
            prices = data['close'].unstack()  # 转换为 date x asset 格式
            
            # 计算动量
            if method == 'return':
                # 简单收益率动量
                momentum = (prices.shift(skip) / prices.shift(skip + lookback) - 1)
            elif method == 'log_return':
                # 对数收益率动量
                momentum = np.log(prices.shift(skip) / prices.shift(skip + lookback))
            else:
                raise ValueError(f"Unknown method: {method}")
                
            # 转换回 MultiIndex 格式
            momentum_series = momentum.stack()
            momentum_series.index.names = ['date', 'asset']
            momentum_series.name = f'momentum_{lookback}d'
            
            # 移除无效值
            momentum_series = momentum_series.dropna()
            
            logger.debug(f"Calculated momentum factor for {len(momentum_series)} observations")
            return momentum_series
            
        except Exception as e:
            logger.error(f"Error calculating momentum factor: {str(e)}")
            raise
            
    def get_required_data(self) -> List[str]:
        """
        获取所需数据字段
        
        Returns:
        --------
        List[str]
            所需数据字段列表
        """
        return ['close']
        
    def get_factor_info(self) -> Dict[str, Any]:
        """
        获取因子信息
        
        Returns:
        --------
        Dict[str, Any]
            因子详细信息
        """
        return {
            'name': 'MomentumFactor',
            'description': '价格动量因子，衡量股票在指定期间的价格变化',
            'category': 'momentum',
            'parameters': {
                'lookback_period': {
                    'description': '回看期（天）',
                    'type': 'int',
                    'default': self.lookback_period,
                    'range': [1, 252]
                },
                'skip_period': {
                    'description': '跳过期（天）',
                    'type': 'int', 
                    'default': self.skip_period,
                    'range': [0, 10]
                },
                'method': {
                    'description': '计算方法',
                    'type': 'str',
                    'default': self.method,
                    'options': ['return', 'log_return']
                }
            },
            'required_data': self.get_required_data(),
            'output_description': '动量因子值，正值表示上涨动量，负值表示下跌动量'
        }


class RSIFactor(BaseFactor):
    """
    相对强弱指数(RSI)因子
    
    计算RSI技术指标作为动量因子。
    """
    
    def __init__(self, period: int = 14, **kwargs):
        """
        初始化RSI因子

        Parameters:
        -----------
        period : int, optional
            RSI计算期间，默认14天
        **kwargs : dict
            其他参数
        """
        # 确保传递name参数
        if 'name' not in kwargs:
            kwargs['name'] = 'RSIFactor'
        if 'description' not in kwargs:
            kwargs['description'] = f'RSI factor with {period}D period'
        if 'category' not in kwargs:
            kwargs['category'] = 'technical'

        super().__init__(**kwargs)
        self.period = period
        
        if period <= 0:
            raise ValueError("period must be positive")
            
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算RSI因子
        
        Parameters:
        -----------
        data : pd.DataFrame
            价格数据，包含 'close' 列
        **params : dict
            运行时参数
            
        Returns:
        --------
        pd.Series
            RSI因子值
        """
        try:
            period = params.get('period', self.period)
            
            # 验证数据
            self.validate_data(data)
            
            if 'close' not in data.columns:
                raise ValueError("Data must contain 'close' column")
                
            # 获取收盘价
            prices = data['close'].unstack()
            
            # 计算价格变化
            delta = prices.diff()
            
            # 分离上涨和下跌
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            # 计算平均收益和损失
            avg_gain = gain.rolling(window=period, min_periods=period).mean()
            avg_loss = loss.rolling(window=period, min_periods=period).mean()
            
            # 计算RSI
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            # 转换为MultiIndex格式
            rsi_series = rsi.stack()
            rsi_series.index.names = ['date', 'asset']
            rsi_series.name = f'rsi_{period}d'
            
            # 移除无效值
            rsi_series = rsi_series.dropna()
            
            logger.debug(f"Calculated RSI factor for {len(rsi_series)} observations")
            return rsi_series
            
        except Exception as e:
            logger.error(f"Error calculating RSI factor: {str(e)}")
            raise
            
    def get_required_data(self) -> List[str]:
        """获取所需数据字段"""
        return ['close']
        
    def get_factor_info(self) -> Dict[str, Any]:
        """获取因子信息"""
        return {
            'name': 'RSIFactor',
            'description': '相对强弱指数，衡量价格变化的速度和幅度',
            'category': 'momentum',
            'parameters': {
                'period': {
                    'description': 'RSI计算期间（天）',
                    'type': 'int',
                    'default': self.period,
                    'range': [2, 50]
                }
            },
            'required_data': self.get_required_data(),
            'output_description': 'RSI值，范围0-100，>70超买，<30超卖'
        }


class PriceVolumeTrendFactor(BaseFactor):
    """
    价量趋势(PVT)因子
    
    结合价格和成交量的动量指标。
    """
    
    def __init__(self, period: int = 20, **kwargs):
        """
        初始化PVT因子

        Parameters:
        -----------
        period : int, optional
            PVT计算周期，默认20
        **kwargs : dict
            其他参数
        """
        # 提取BaseFactor需要的参数
        base_kwargs = {
            'name': kwargs.get('name', 'PriceVolumeTrendFactor'),
            'description': kwargs.get('description', 'Price Volume Trend factor'),
            'category': kwargs.get('category', 'technical')
        }

        super().__init__(**base_kwargs)
        self.period = period
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算PVT因子
        
        Parameters:
        -----------
        data : pd.DataFrame
            价格和成交量数据，包含 'close' 和 'volume' 列
        **params : dict
            运行时参数
            
        Returns:
        --------
        pd.Series
            PVT因子值
        """
        try:
            # 验证数据
            self.validate_data(data)
            
            required_cols = ['close', 'volume']
            for col in required_cols:
                if col not in data.columns:
                    raise ValueError(f"Data must contain '{col}' column")
                    
            # 获取价格和成交量
            prices = data['close'].unstack()
            volumes = data['volume'].unstack()
            
            # 计算价格变化率
            price_change_pct = prices.pct_change()
            
            # 计算PVT
            pvt = (price_change_pct * volumes).cumsum()

            # 获取period参数
            period = params.get('period', self.period)

            # 计算period天的PVT变化率作为因子值
            pvt_factor = pvt.pct_change(periods=period)

            # 转换为MultiIndex格式
            pvt_series = pvt_factor.stack()
            pvt_series.index.names = ['date', 'asset']
            pvt_series.name = 'pvt'

            # 移除无效值
            pvt_series = pvt_series.dropna()
            
            logger.debug(f"Calculated PVT factor for {len(pvt_series)} observations")
            return pvt_series
            
        except Exception as e:
            logger.error(f"Error calculating PVT factor: {str(e)}")
            raise
            
    def get_required_data(self) -> List[str]:
        """获取所需数据字段"""
        return ['close', 'volume']
        
    def get_factor_info(self) -> Dict[str, Any]:
        """获取因子信息"""
        return {
            'name': 'PriceVolumeTrendFactor',
            'description': '价量趋势指标，结合价格变化和成交量',
            'category': 'momentum',
            'parameters': {'period': self.period},
            'required_data': self.get_required_data(),
            'output_description': 'PVT变化率，正值表示价量配合上涨，负值表示价量配合下跌'
        }
