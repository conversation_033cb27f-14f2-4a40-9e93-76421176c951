class RiskManager:
    """
    风控管理器，支持整体仓位和个体仓位控制。
    """

    def __init__(self, max_total_ratio=1.0, symbol_max_ratio=None):
        """
        :param max_total_ratio: 总持仓市值/账户市值最大比例
        :param symbol_max_ratio: dict, 各标的最大持仓比例，如 {'000001.SZ': 0.3}
        """
        self.max_total_ratio = max_total_ratio
        self.symbol_max_ratio = symbol_max_ratio or {}
        print(f"初始化风控管理器: max_total_ratio={max_total_ratio}, symbol_max_ratio={symbol_max_ratio}")

    def check_ratio(self, ratio, max_ratio):
        """
        检查单个比例是否超限
        """
        if max_ratio <= 0:
            return False
        return ratio <= max_ratio

    def check_total_position(self, total_position_value, account_value):
        """
        检查整体仓位是否超限
        """
        if account_value <= 0:
            return False
        ratio = total_position_value / account_value
        allow = self.check_ratio(ratio, self.max_total_ratio)
        return allow

    def check_symbol_position(self, symbol, symbol_position_value, account_value):
        """
        检查单标的仓位是否超限
        """
        if account_value <= 0:
            return False
        # 如果没有单标仓位设置，默认最大0.7
        max_ratio = self.symbol_max_ratio.get(symbol, 0.7)
        ratio = symbol_position_value / account_value
        allow = self.check_ratio(ratio, max_ratio)
        return allow

    def check(self, symbol, signal, symbol_position_value, total_position_value, account_value):
        """
        综合检查整体和个体仓位
        """
        # print(f"开始风控检查: symbol={symbol}, symbol_position_value={symbol_position_value}, "
        #       f"total_position_value={total_position_value}, account_value={account_value}")
        if signal > 0:
            allow =  self.check_total_position(total_position_value, account_value) and \
               self.check_symbol_position(symbol, symbol_position_value, account_value)
        else:
            allow = True
        return allow
