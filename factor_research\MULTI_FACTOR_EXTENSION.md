# 多因子研究扩展设计文档

## 1. 多因子框架架构

### 1.1 扩展架构图

```
Multi-Factor Framework
├── Factor Combination
│   ├── Linear Combination      # 线性组合
│   ├── Non-linear Combination  # 非线性组合
│   └── Machine Learning        # 机器学习组合
├── Weight Allocation
│   ├── Equal Weight           # 等权重
│   ├── IC Weight             # IC加权
│   ├── Risk Parity           # 风险平价
│   └── Optimization Based    # 优化权重
├── Risk Management
│   ├── Factor Exposure       # 因子暴露
│   ├── Risk Attribution      # 风险归因
│   └── Portfolio Risk        # 组合风险
└── Portfolio Construction
    ├── Long-Short Strategy   # 多空策略
    ├── Long-Only Strategy    # 多头策略
    └── Market Neutral        # 市场中性
```

### 1.2 核心扩展接口

```python
class MultiFactorResearch:
    """
    多因子研究框架主类
    """
    
    def __init__(self, single_factor_research: FactorResearch):
        """
        基于单因子研究框架构建多因子研究
        
        Parameters:
        -----------
        single_factor_research : FactorResearch
            单因子研究框架实例
        """
        self.single_factor = single_factor_research
        self.factor_combiner = FactorCombiner()
        self.weight_allocator = WeightAllocator()
        self.risk_manager = RiskManager()
        self.portfolio_constructor = PortfolioConstructor()
        
    def combine_factors(self, factor_data_dict: Dict[str, pd.Series], 
                       method: str = "linear", **kwargs) -> pd.Series:
        """
        组合多个因子
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            {factor_name: factor_values} 字典
        method : str
            组合方法：'linear', 'pca', 'ml', 'rank'
        **kwargs : dict
            组合参数
            
        Returns:
        --------
        pd.Series
            组合因子值
        """
        
    def optimize_weights(self, factor_data_dict: Dict[str, pd.Series],
                        method: str = "ic_weight", **kwargs) -> Dict[str, float]:
        """
        优化因子权重
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        method : str
            权重优化方法
        **kwargs : dict
            优化参数
            
        Returns:
        --------
        Dict[str, float]
            {factor_name: weight} 权重字典
        """
        
    def construct_portfolio(self, combined_factor: pd.Series,
                           method: str = "long_short", **kwargs) -> pd.DataFrame:
        """
        构建投资组合
        
        Parameters:
        -----------
        combined_factor : pd.Series
            组合因子值
        method : str
            组合构建方法
        **kwargs : dict
            构建参数
            
        Returns:
        --------
        pd.DataFrame
            投资组合权重，MultiIndex (date, asset)
        """
```

## 2. 因子组合方法

### 2.1 FactorCombiner - 因子组合器

```python
class FactorCombiner:
    """
    因子组合器，支持多种因子组合方法
    """
    
    def linear_combination(self, factor_data_dict: Dict[str, pd.Series],
                          weights: Dict[str, float] = None) -> pd.Series:
        """
        线性组合因子
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        weights : Dict[str, float], optional
            因子权重，默认等权重
            
        Returns:
        --------
        pd.Series
            线性组合因子
        """
        if weights is None:
            weights = {name: 1.0/len(factor_data_dict) 
                      for name in factor_data_dict.keys()}
        
        # 标准化因子
        normalized_factors = {}
        for name, factor_data in factor_data_dict.items():
            normalized_factors[name] = self._standardize_factor(factor_data)
        
        # 线性组合
        combined_factor = pd.Series(0, index=list(factor_data_dict.values())[0].index)
        for name, factor_data in normalized_factors.items():
            combined_factor += weights[name] * factor_data
            
        return combined_factor
        
    def pca_combination(self, factor_data_dict: Dict[str, pd.Series],
                       n_components: int = 1) -> pd.Series:
        """
        PCA主成分组合
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        n_components : int
            主成分数量
            
        Returns:
        --------
        pd.Series
            PCA组合因子
        """
        from sklearn.decomposition import PCA
        
        # 构建因子矩阵
        factor_df = pd.DataFrame(factor_data_dict)
        factor_df = factor_df.dropna()
        
        # PCA降维
        pca = PCA(n_components=n_components)
        pca_result = pca.fit_transform(factor_df.values)
        
        # 返回第一主成分
        combined_factor = pd.Series(pca_result[:, 0], index=factor_df.index)
        
        return combined_factor
        
    def rank_combination(self, factor_data_dict: Dict[str, pd.Series],
                        weights: Dict[str, float] = None) -> pd.Series:
        """
        排序组合（基于因子排名）
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        weights : Dict[str, float], optional
            因子权重
            
        Returns:
        --------
        pd.Series
            排序组合因子
        """
        if weights is None:
            weights = {name: 1.0/len(factor_data_dict) 
                      for name in factor_data_dict.keys()}
        
        # 计算因子排名
        ranked_factors = {}
        for name, factor_data in factor_data_dict.items():
            ranked_factors[name] = factor_data.groupby(level=0).rank(pct=True)
        
        # 加权组合排名
        combined_rank = pd.Series(0, index=list(factor_data_dict.values())[0].index)
        for name, rank_data in ranked_factors.items():
            combined_rank += weights[name] * rank_data
            
        return combined_rank
        
    def ml_combination(self, factor_data_dict: Dict[str, pd.Series],
                      target_returns: pd.Series, model_type: str = "ridge") -> pd.Series:
        """
        机器学习组合
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        target_returns : pd.Series
            目标收益率
        model_type : str
            模型类型：'ridge', 'lasso', 'random_forest', 'xgboost'
            
        Returns:
        --------
        pd.Series
            机器学习组合因子
        """
        from sklearn.linear_model import Ridge, Lasso
        from sklearn.ensemble import RandomForestRegressor
        
        # 构建特征矩阵
        factor_df = pd.DataFrame(factor_data_dict)
        
        # 对齐数据
        aligned_data = pd.concat([factor_df, target_returns], axis=1, join='inner')
        X = aligned_data.iloc[:, :-1]
        y = aligned_data.iloc[:, -1]
        
        # 选择模型
        if model_type == "ridge":
            model = Ridge(alpha=1.0)
        elif model_type == "lasso":
            model = Lasso(alpha=0.1)
        elif model_type == "random_forest":
            model = RandomForestRegressor(n_estimators=100, random_state=42)
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
        
        # 训练模型
        model.fit(X, y)
        
        # 预测组合因子
        combined_factor = pd.Series(model.predict(factor_df.dropna()), 
                                   index=factor_df.dropna().index)
        
        return combined_factor
        
    def _standardize_factor(self, factor_data: pd.Series) -> pd.Series:
        """标准化因子值"""
        return factor_data.groupby(level=0).apply(
            lambda x: (x - x.mean()) / x.std()
        )
```

## 3. 权重分配方法

### 3.1 WeightAllocator - 权重分配器

```python
class WeightAllocator:
    """
    权重分配器，支持多种权重分配方法
    """
    
    def equal_weight(self, factor_names: List[str]) -> Dict[str, float]:
        """
        等权重分配
        
        Parameters:
        -----------
        factor_names : List[str]
            因子名称列表
            
        Returns:
        --------
        Dict[str, float]
            等权重字典
        """
        weight = 1.0 / len(factor_names)
        return {name: weight for name in factor_names}
        
    def ic_weight(self, factor_data_dict: Dict[str, pd.Series],
                  returns: pd.Series, lookback: int = 252) -> Dict[str, float]:
        """
        基于IC的权重分配
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        returns : pd.Series
            收益率数据
        lookback : int
            回看期
            
        Returns:
        --------
        Dict[str, float]
            IC权重字典
        """
        ic_values = {}
        
        for name, factor_data in factor_data_dict.items():
            # 计算滚动IC
            ic_series = self._calculate_rolling_ic(factor_data, returns, lookback)
            ic_values[name] = ic_series.mean()
        
        # 归一化权重
        total_ic = sum(abs(ic) for ic in ic_values.values())
        if total_ic == 0:
            return self.equal_weight(list(factor_data_dict.keys()))
        
        weights = {name: abs(ic) / total_ic for name, ic in ic_values.items()}
        
        return weights
        
    def risk_parity_weight(self, factor_data_dict: Dict[str, pd.Series]) -> Dict[str, float]:
        """
        风险平价权重分配
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
            
        Returns:
        --------
        Dict[str, float]
            风险平价权重字典
        """
        # 计算因子协方差矩阵
        factor_df = pd.DataFrame(factor_data_dict)
        cov_matrix = factor_df.cov()
        
        # 计算风险平价权重
        inv_vol = 1.0 / np.sqrt(np.diag(cov_matrix))
        weights = inv_vol / inv_vol.sum()
        
        return dict(zip(factor_df.columns, weights))
        
    def optimization_weight(self, factor_data_dict: Dict[str, pd.Series],
                           returns: pd.Series, method: str = "max_sharpe") -> Dict[str, float]:
        """
        基于优化的权重分配
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            因子数据字典
        returns : pd.Series
            收益率数据
        method : str
            优化目标：'max_sharpe', 'min_variance', 'max_return'
            
        Returns:
        --------
        Dict[str, float]
            优化权重字典
        """
        from scipy.optimize import minimize
        
        # 构建因子收益矩阵
        factor_returns = {}
        for name, factor_data in factor_data_dict.items():
            factor_returns[name] = self._calculate_factor_returns(factor_data, returns)
        
        factor_returns_df = pd.DataFrame(factor_returns)
        
        # 计算期望收益和协方差矩阵
        expected_returns = factor_returns_df.mean()
        cov_matrix = factor_returns_df.cov()
        
        n_factors = len(factor_data_dict)
        
        def objective(weights):
            portfolio_return = np.dot(weights, expected_returns)
            portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
            
            if method == "max_sharpe":
                return -portfolio_return / np.sqrt(portfolio_variance)
            elif method == "min_variance":
                return portfolio_variance
            elif method == "max_return":
                return -portfolio_return
        
        # 约束条件
        constraints = ({'type': 'eq', 'fun': lambda x: np.sum(x) - 1})
        bounds = tuple((0, 1) for _ in range(n_factors))
        
        # 优化
        result = minimize(objective, 
                         x0=np.array([1/n_factors] * n_factors),
                         method='SLSQP',
                         bounds=bounds,
                         constraints=constraints)
        
        return dict(zip(factor_data_dict.keys(), result.x))
        
    def _calculate_rolling_ic(self, factor_data: pd.Series, returns: pd.Series, 
                             window: int) -> pd.Series:
        """计算滚动IC"""
        aligned_data = pd.concat([factor_data, returns], axis=1, join='inner')
        
        def rolling_corr(data):
            if len(data) < 2:
                return np.nan
            return data.iloc[:, 0].corr(data.iloc[:, 1])
        
        ic_series = aligned_data.rolling(window=window).apply(
            rolling_corr, raw=False
        ).iloc[:, 0]
        
        return ic_series
        
    def _calculate_factor_returns(self, factor_data: pd.Series, 
                                 returns: pd.Series) -> pd.Series:
        """计算因子收益"""
        # 简化实现：使用因子值与收益率的相关性作为因子收益
        aligned_data = pd.concat([factor_data, returns], axis=1, join='inner')
        
        factor_returns = aligned_data.groupby(level=0).apply(
            lambda x: x.iloc[:, 0].corr(x.iloc[:, 1])
        )
        
        return factor_returns
```

## 4. 风险管理

### 4.1 RiskManager - 风险管理器

```python
class RiskManager:
    """
    风险管理器，提供因子风险分析和管理功能
    """
    
    def calculate_factor_exposure(self, portfolio_weights: pd.DataFrame,
                                 factor_loadings: pd.DataFrame) -> pd.DataFrame:
        """
        计算组合的因子暴露
        
        Parameters:
        -----------
        portfolio_weights : pd.DataFrame
            组合权重，MultiIndex (date, asset)
        factor_loadings : pd.DataFrame
            因子载荷，MultiIndex (date, asset), columns为因子名
            
        Returns:
        --------
        pd.DataFrame
            因子暴露，index为date，columns为因子名
        """
        factor_exposure = {}
        
        for date in portfolio_weights.index.get_level_values(0).unique():
            date_weights = portfolio_weights.loc[date]
            date_loadings = factor_loadings.loc[date]
            
            # 计算该日期的因子暴露
            exposure = date_weights.T @ date_loadings
            factor_exposure[date] = exposure
        
        return pd.DataFrame(factor_exposure).T
        
    def risk_attribution(self, portfolio_returns: pd.Series,
                        factor_returns: pd.DataFrame,
                        factor_loadings: pd.DataFrame) -> Dict:
        """
        风险归因分析
        
        Parameters:
        -----------
        portfolio_returns : pd.Series
            组合收益率
        factor_returns : pd.DataFrame
            因子收益率
        factor_loadings : pd.DataFrame
            因子载荷
            
        Returns:
        --------
        Dict
            风险归因结果
        """
        # 计算因子贡献
        factor_contributions = {}
        
        for factor in factor_returns.columns:
            factor_contrib = (factor_loadings[factor] * factor_returns[factor]).sum()
            factor_contributions[factor] = factor_contrib
        
        # 计算特异性风险
        total_factor_contrib = sum(factor_contributions.values())
        idiosyncratic_risk = portfolio_returns.var() - total_factor_contrib
        
        return {
            'factor_contributions': factor_contributions,
            'idiosyncratic_risk': idiosyncratic_risk,
            'total_risk': portfolio_returns.var()
        }
```

## 5. 组合构建

### 5.1 PortfolioConstructor - 组合构建器

```python
class PortfolioConstructor:
    """
    组合构建器，基于组合因子构建投资组合
    """
    
    def long_short_portfolio(self, combined_factor: pd.Series,
                           long_pct: float = 0.2, short_pct: float = 0.2) -> pd.DataFrame:
        """
        构建多空组合
        
        Parameters:
        -----------
        combined_factor : pd.Series
            组合因子值
        long_pct : float
            做多比例
        short_pct : float
            做空比例
            
        Returns:
        --------
        pd.DataFrame
            组合权重
        """
        portfolio_weights = []
        
        for date in combined_factor.index.get_level_values(0).unique():
            date_factor = combined_factor.loc[date].sort_values(ascending=False)
            
            n_assets = len(date_factor)
            n_long = int(n_assets * long_pct)
            n_short = int(n_assets * short_pct)
            
            # 构建权重
            weights = pd.Series(0.0, index=date_factor.index)
            
            # 做多权重
            if n_long > 0:
                long_assets = date_factor.head(n_long).index
                weights[long_assets] = 1.0 / n_long
            
            # 做空权重
            if n_short > 0:
                short_assets = date_factor.tail(n_short).index
                weights[short_assets] = -1.0 / n_short
            
            weights.name = date
            portfolio_weights.append(weights)
        
        return pd.concat(portfolio_weights, axis=1).T
        
    def long_only_portfolio(self, combined_factor: pd.Series,
                           top_pct: float = 0.2) -> pd.DataFrame:
        """
        构建纯多头组合
        
        Parameters:
        -----------
        combined_factor : pd.Series
            组合因子值
        top_pct : float
            选股比例
            
        Returns:
        --------
        pd.DataFrame
            组合权重
        """
        portfolio_weights = []
        
        for date in combined_factor.index.get_level_values(0).unique():
            date_factor = combined_factor.loc[date].sort_values(ascending=False)
            
            n_assets = len(date_factor)
            n_selected = int(n_assets * top_pct)
            
            weights = pd.Series(0.0, index=date_factor.index)
            
            if n_selected > 0:
                selected_assets = date_factor.head(n_selected).index
                weights[selected_assets] = 1.0 / n_selected
            
            weights.name = date
            portfolio_weights.append(weights)
        
        return pd.concat(portfolio_weights, axis=1).T
```

这个多因子扩展设计为单因子研究框架提供了完整的多因子研究能力，支持因子组合、权重优化、风险管理和组合构建等核心功能。
