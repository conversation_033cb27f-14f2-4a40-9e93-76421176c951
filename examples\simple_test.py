#!/usr/bin/env python3
"""
简单的因子研究框架测试

验证框架的基本功能是否正常工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """测试模块导入"""
    logger.info("Testing imports...")
    
    try:
        from factor_research.factors.momentum import MomentumFactor, RSIFactor
        from factor_research.core.analyzer import FactorAnalyzer
        from factor_research.utils.config import FactorConfig
        from factor_research import FactorResearch
        
        logger.info("✓ All imports successful")
        return True
    except Exception as e:
        logger.error(f"✗ Import failed: {e}")
        return False

def test_factor_creation():
    """测试因子创建"""
    logger.info("Testing factor creation...")
    
    try:
        from factor_research.factors.momentum import MomentumFactor, RSIFactor
        
        # 创建动量因子
        momentum = MomentumFactor(lookback_period=20, skip_period=1)
        logger.info(f"✓ MomentumFactor created: {momentum.name}")
        
        # 创建RSI因子
        rsi = RSIFactor(period=14)
        logger.info(f"✓ RSIFactor created: {rsi.name}")
        
        return True
    except Exception as e:
        logger.error(f"✗ Factor creation failed: {e}")
        return False

def test_config():
    """测试配置系统"""
    logger.info("Testing configuration system...")
    
    try:
        from factor_research.utils.config import FactorConfig
        
        # 创建配置
        config_data = {
            'analysis': {
                'default_periods': [1, 5, 10],
                'default_quantiles': 5
            },
            'visualization': {
                'figure_size': [12, 8]
            }
        }
        
        config = FactorConfig(config_data)
        
        # 测试配置访问
        periods = config.get('analysis.default_periods')
        quantiles = config.get('analysis.default_quantiles')
        
        logger.info(f"✓ Config created and accessed: periods={periods}, quantiles={quantiles}")
        return True
    except Exception as e:
        logger.error(f"✗ Config test failed: {e}")
        return False

def test_analyzer_creation():
    """测试分析器创建"""
    logger.info("Testing analyzer creation...")
    
    try:
        from factor_research.core.analyzer import FactorAnalyzer
        
        # 创建分析器（不使用数据适配器）
        analyzer = FactorAnalyzer(data_adapter=None)
        
        logger.info("✓ FactorAnalyzer created successfully")
        return True
    except Exception as e:
        logger.error(f"✗ Analyzer creation failed: {e}")
        return False

def test_factor_research_creation():
    """测试主框架创建"""
    logger.info("Testing FactorResearch creation...")
    
    try:
        from factor_research import FactorResearch
        
        # 创建研究框架
        research = FactorResearch()
        
        logger.info("✓ FactorResearch created successfully")
        return True
    except Exception as e:
        logger.error(f"✗ FactorResearch creation failed: {e}")
        return False

def test_factor_registration():
    """测试因子注册"""
    logger.info("Testing factor registration...")
    
    try:
        from factor_research import FactorResearch
        from factor_research.factors.momentum import MomentumFactor, RSIFactor
        
        # 创建研究框架
        research = FactorResearch()
        
        # 注册因子
        research.register_factor(MomentumFactor, lookback_period=20, skip_period=1)
        research.register_factor(RSIFactor, period=14)
        
        # 检查注册的因子
        registered_factors = research.list_factors()
        logger.info(f"✓ Factors registered: {registered_factors}")
        
        return True
    except Exception as e:
        logger.error(f"✗ Factor registration failed: {e}")
        return False

def test_mock_data_creation():
    """测试模拟数据创建"""
    logger.info("Testing mock data creation...")
    
    try:
        # 创建模拟因子数据
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        assets = ['AAPL', 'MSFT', 'GOOGL']
        
        # 创建MultiIndex
        index = pd.MultiIndex.from_product([dates, assets], names=['date', 'asset'])
        
        # 生成随机因子值
        np.random.seed(42)
        factor_values = np.random.normal(0, 1, len(index))
        
        # 创建因子数据Series
        factor_data = pd.Series(factor_values, index=index, name='test_factor')
        
        logger.info(f"✓ Mock factor data created: {len(factor_data)} observations")
        logger.info(f"  Sample data:\n{factor_data.head()}")
        
        return factor_data
    except Exception as e:
        logger.error(f"✗ Mock data creation failed: {e}")
        return None

def test_simple_analysis():
    """测试简单分析"""
    logger.info("Testing simple analysis...")
    
    try:
        from factor_research.core.analyzer import FactorAnalyzer
        
        # 创建模拟数据
        factor_data = test_mock_data_creation()
        if factor_data is None:
            return False
        
        # 创建分析器
        analyzer = FactorAnalyzer(data_adapter=None)
        
        # 执行简单的IC计算
        dates = factor_data.index.get_level_values(0).unique()
        assets = factor_data.index.get_level_values(1).unique()
        
        # 创建简化的因子数据框架
        data_list = []
        np.random.seed(42)
        
        for date in dates[:30]:  # 只取前30天
            for asset in assets:
                factor_value = factor_data.loc[(date, asset)]
                return_1d = np.random.normal(0.001, 0.02)
                return_5d = np.random.normal(0.005, 0.05)
                
                data_list.append({
                    'date': date,
                    'asset': asset,
                    'factor': factor_value,
                    '1D': return_1d,
                    '5D': return_5d,
                    'factor_quantile': np.random.randint(1, 6)
                })
        
        # 创建DataFrame
        factor_data_clean = pd.DataFrame(data_list)
        factor_data_clean = factor_data_clean.set_index(['date', 'asset'])
        
        # 计算IC
        ic_results = analyzer._calculate_simple_ic(factor_data_clean)
        
        logger.info(f"✓ Simple IC calculation successful")
        logger.info(f"  IC results shape: {ic_results.shape}")
        logger.info(f"  IC mean: {ic_results.mean()}")
        
        return True
    except Exception as e:
        logger.error(f"✗ Simple analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("Starting simple factor research framework tests...")
    logger.info("="*60)
    
    tests = [
        ("Import Test", test_imports),
        ("Factor Creation Test", test_factor_creation),
        ("Config Test", test_config),
        ("Analyzer Creation Test", test_analyzer_creation),
        ("FactorResearch Creation Test", test_factor_research_creation),
        ("Factor Registration Test", test_factor_registration),
        ("Mock Data Creation Test", lambda: test_mock_data_creation() is not None),
        ("Simple Analysis Test", test_simple_analysis),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{test_name}:")
        logger.info("-" * 40)
        
        try:
            if test_func():
                passed += 1
                logger.info(f"✓ {test_name} PASSED")
            else:
                logger.error(f"✗ {test_name} FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name} FAILED with exception: {e}")
    
    logger.info("\n" + "="*60)
    logger.info(f"TEST SUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Framework is working correctly.")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
