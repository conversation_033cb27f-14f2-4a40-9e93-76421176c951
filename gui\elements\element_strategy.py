#! /usr/bin/env python
# -*- encoding: utf-8 -*-

import os
import json
import wx
import wx.adv
import wx.gizmos
import wx.grid
import wx.html2
import yaml
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTreeWidget, QTreeWidgetItem
from common.config import STRATEGY_DIR


class ElementStrategy(QWidget):
    def __init__(self, parent=None, root_name="择时策略"):
        super().__init__(parent)
        self.root_name = root_name
        self._init_ui()
        self.refDataShow(self._load_strategies())

    def _init_ui(self):
        layout = QVBoxLayout(self)
        self.tree = QTreeWidget()
        self.tree.setColumnCount(2)
        self.tree.setHeaderLabels(["名称", "函数"])
        layout.addWidget(self.tree)
        self.setLayout(layout)

    def _load_strategies(self):
        if self.root_name == "择时策略":
            strategies_yaml_path = STRATEGY_DIR / "timing_strategy.yaml"
        elif self.root_name == "选股策略":
            strategies_yaml_path = STRATEGY_DIR / "equity_sel_strategy.yaml"
        else:
            return {}
        if strategies_yaml_path.exists():
            try:
                with open(strategies_yaml_path, "r", encoding="utf-8") as f:
                    data = yaml.safe_load(f)
                return data
            except Exception as e:
                print("读取策略列表 YAML 文件失败:", strategies_yaml_path, e)
                return {}
        else:
            print("策略列表文件不存在:", strategies_yaml_path)
            return {}

    def refDataShow(self, newDatas):
        self.tree.clear()
        if newDatas is not None:
            root = QTreeWidgetItem([self.root_name, ""])
            self.tree.addTopLevelItem(root)
            for cityID in newDatas.keys():
                child = QTreeWidgetItem([f"{cityID} (共{len(newDatas[cityID])}个)", ""])
                root.addChild(child)
                lastList = newDatas.get(cityID, [])
                for index, college in enumerate(lastList):
                    last = QTreeWidgetItem(
                        [college.get("名称", ""), college.get("函数", "")]
                    )
                    child.addChild(last)
            self.tree.expandAll()
