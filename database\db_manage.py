from .quant_db import QuantDB
from .hist_db import HistDB
from .realtime_db import RealTimeDB
from .snapshot_db import SnapshotDB
from engine.data_engine.data_engine import DataEngine
import os
import threading
import time
import datetime

class DBManage(object):
    def __init__(self, account_manage):
        self.account_manage = account_manage
        self.data_engine = DataEngine()
        self.quant_db = QuantDB()
        self.hist_db = HistDB(self.data_engine)
        self.realtime_db = RealTimeDB(self.data_engine)
        self.snapshot_db = SnapshotDB()

    def init_database(self):
        """
        数据库初始化：没有数据库时创建数据库文件和基础表结构
        """
        # 确保数据库文件存在
        if not os.path.exists(self.quant_db.db_path):
            # 创建空数据库文件并初始化表
            self.quant_db.conn.close()  # 触发文件创建
        # 初始化实时数据表
        self.realtime_db.init_realtime_tables()

    def update_hist_data_background(self):
        """
        后台线程：根据所有账户股票池，更新或下载股票历史数据
        """
        def update_task():
            accounts = self.account_manage._list_account()
            for account_name in accounts:
                info = self.account_manage._load_account(account_name)
                if not info:
                    continue
                equity_pool = info.get("equity_pool", [])
                adjust = info.get("adjust", "前复权")
                self.hist_db.update_hist_data(equity_pool, adjust=adjust)
        threading.Thread(target=update_task, daemon=True).start()

    def update_realtime_once(self):
        """
        单次刷新实时数据并更新到数据库中，打印耗时用于调试。
        如果实时数据库没有数据，则自动执行一次刷新。
        支持多标的/多表实时数据库，逐个判断并刷新。
        """
        # 检查所有实时数据库表（如有多个标的/表）是否有数据
        need_refresh = False
        try:
            # 假设有方法获取所有实时表名
            table_names = self.realtime_db.get_all_realtime_tables() if hasattr(self.realtime_db, "get_all_realtime_tables") else []
            if not table_names:
                # 如果没有表名方法，默认只检查主表
                table_names = [None]
            for table in table_names:
                df = self.realtime_db.query_realtime(table=table) if table else self.realtime_db.query_realtime()
                if df is None or df.empty:
                    print(f"[DBManage] 实时数据库表 {table or 'default'} 无数据，自动刷新。")
                    need_refresh = True
                    try:
                        self.realtime_db.refresh_realtime() if table else self.realtime_db.refresh_realtime(self.data_engine)
                    except Exception as e:
                        print(f"[DBManage] 首次实时数据刷新异常: {e}")
        except Exception as e:
            print(f"[DBManage] 检查实时数据库异常: {e}")
            need_refresh = True
            try:
                self.realtime_db.refresh_realtime(self.data_engine)
            except Exception as e2:
                print(f"[DBManage] 首次实时数据刷新异常: {e2}")

    def update_realtime_data_periodic(self, interval_sec=60, end_time="15:00", window_min=30):
        """
        周期性刷新实时数据并更新到数据库中，仅在尾盘前window_min分钟内每interval_sec秒刷新一次
        :param interval_sec: 刷新周期（秒）
        :param end_time: 收盘时间（如"15:00"）
        :param window_min: 距离收盘的分钟数，窗口内才刷新
        """
        def refresh_task():
            while True:
                now = datetime.datetime.now()
                today_end = now.replace(hour=int(end_time.split(":")[0]), minute=int(end_time.split(":")[1]), second=0, microsecond=0)
                window_start = today_end - datetime.timedelta(minutes=window_min)
                if window_start <= now < today_end:
                    try:
                        self.realtime_db.refresh_realtime()
                    except Exception as e:
                        print(f"[DBManage] 实时数据刷新异常: {e}")
                # 其余时间不刷新
                time.sleep(interval_sec)
        threading.Thread(target=refresh_task, daemon=True).start()

    def main_loop(self):
        """
        启动数据库相关的后台任务
        """
        # 初始化数据库
        self.init_database()

        # 启动后台历史数据更新
        self.update_hist_data_background()

        # 单次刷新实时数据
        self.update_realtime_once()
        # 启动尾盘前30分钟每分钟刷新一次实时数据
        self.update_realtime_data_periodic(interval_sec=60, end_time="15:00", window_min=30)



