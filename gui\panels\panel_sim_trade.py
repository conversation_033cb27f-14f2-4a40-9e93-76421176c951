from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QTableWidget,
    QTableWidgetItem, QCheckBox, QMessageBox, QSplitter, QTabWidget
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from common.config import PERIOD_MAP, SIM_TRADE_RESULT_DIR
from gui.elements.element_account import ElementAccount
from gui.widgets.widget_web import WebPanel
from engine.trade_engine.trade_engine import TradeEngine
from portfolio.account.account_manage import AccountManage
from database.realtime_db import RealTimeDB
from database.hist_db import HistDB

class SimTradeThread(QThread):
    finished = pyqtSignal()
    def __init__(self, panel):
        super().__init__()
        self.panel = panel

    def run(self):
        self.panel.run_sim_trade_logic()
        self.finished.emit()

class PanelSimTrade(QWidget):
    def __init__(self, parent=None, displaySize=(1600, 900), *args, **kw):
        super().__init__(parent)
        self.position_manager = kw.get("position_manager")
        self.account_manage = kw.get("account_manage")
        self.trade_engine = kw.get("trade_engine")
        self.db_manage = kw.get("db_manage")
        self.displaySize = displaySize
        self._init_ui()

    def _init_ui(self):
        # 外层垂直布局
        self.outer_vbox = QVBoxLayout(self)
        # 顶部水平分割：左侧账户管理/信息，右侧账户表现/K线图+股票池
        splitter = QSplitter(Qt.Horizontal)
        # 左侧垂直布局
        left_widget = QWidget()
        left_vbox = QVBoxLayout(left_widget)
        # 账户管理、账户信息
        self.element_account = ElementAccount(left_widget, account_manage=self.account_manage)
        left_widget.setLayout(left_vbox)
        left_vbox.addWidget(self.element_account)  # <-- 修正：ElementAccount必须继承自QWidget
        splitter.addWidget(left_widget)
        # 右侧垂直布局
        right_widget = QWidget()
        right_vbox = QVBoxLayout(right_widget)
        # 账户表现/K线图
        self.web_tabs = QTabWidget(right_widget)
        self.return_rate_panel = WebPanel(self.web_tabs)
        self.kline_panel = WebPanel(self.web_tabs)
        self.web_tabs.addTab(self.return_rate_panel, "回报率")
        self.web_tabs.addTab(self.kline_panel, "K线图")
        right_vbox.addWidget(self.web_tabs, stretch=2)
        # 股票池/持仓表格
        self.equity_table = QTableWidget(0, 4, right_widget)
        self.equity_table.setHorizontalHeaderLabels(["代码", "名称", "持仓数量", "最新价"])
        self.equity_table.itemSelectionChanged.connect(self.on_equity_selection_changed)
        right_vbox.addWidget(self.equity_table, stretch=1)
        splitter.addWidget(right_widget)
        splitter.setStretchFactor(0, 0)
        splitter.setStretchFactor(1, 1)
        self.outer_vbox.addWidget(splitter, stretch=1)
        # 底部按钮和测试模式
        btn_hbox = QHBoxLayout()
        btn_hbox.addStretch(1)
        self.chk_test_mode = QCheckBox("测试模式")
        btn_hbox.addWidget(self.chk_test_mode)
        self.combo_test_mode = QComboBox()
        self.combo_test_mode.addItems(["实时数据测试", "历史数据测试"])
        self.combo_test_mode.setEnabled(False)
        btn_hbox.addWidget(self.combo_test_mode)
        self.chk_test_mode.stateChanged.connect(self._on_toggle_test_mode)
        self.btn_run = QPushButton("开始交易")
        self.btn_stop = QPushButton("结束交易")
        btn_hbox.addWidget(self.btn_run)
        btn_hbox.addWidget(self.btn_stop)
        self.outer_vbox.addLayout(btn_hbox)
        self.btn_run.clicked.connect(self.on_run_sim_trade)
        self.btn_stop.clicked.connect(self.on_stop_sim_trade)
        self.btn_stop.setEnabled(False)
        self.element_account.account_list.currentTextChanged.connect(self.on_select_account)

    def _on_toggle_test_mode(self, state):
        self.combo_test_mode.setEnabled(state == Qt.Checked)

    def on_run_sim_trade(self):
        # 运行测试前初始化trade_engine
        if hasattr(self, '_init_trade_engine'):
            if self._init_trade_engine() is False:
                return
        self.btn_run.setEnabled(False)
        self.btn_stop.setEnabled(True)
        self.sim_thread = SimTradeThread(self)
        self.sim_thread.finished.connect(self.on_sim_trade_finished)
        self.sim_thread.start()

    def run_sim_trade_logic(self):
        is_test = self.chk_test_mode.isChecked()
        test_mode = self.combo_test_mode.currentText() if is_test else None
        if is_test and test_mode == "实时数据测试":
            self.trade_engine.test_sim_trade(n=3)
        elif is_test and test_mode == "历史数据测试":
            self.trade_engine.test_sim_trade_with_hist(n=100)
        else:
            self.trade_engine.run_trade()

    def on_sim_trade_finished(self):
        self.btn_run.setEnabled(True)
        self.btn_stop.setEnabled(False)
        self._update_sim_trade_panel(self.trade_engine.account_profile)

    def on_stop_sim_trade(self):
        # 这里只是示例，实际应通知trade_engine安全停止
        self.btn_run.setEnabled(True)
        self.btn_stop.setEnabled(False)
        QMessageBox.information(self, "提示", "模拟交易已结束")
        self._update_sim_trade_panel(self.trade_engine.account_profile)

    def _update_sim_trade_panel(self, account_data):
        if not account_data:
            return
        # 账户信息
        info_str = self._get_account_info_str(account_data)
        # 若ElementAccount有账户信息文本框则更新
        if hasattr(self.element_account, "account_info_text"):
            self.element_account.account_info_text.setText(info_str)
        # 股票池/持仓表格
        self.equity_table.setRowCount(0)
        equity_pool = account_data.get("equity_pool", [])
        positions = account_data.get("positions", {})
        price_map = account_data.get("price_map", {})
        for item in equity_pool:
            if isinstance(item, dict):
                code = item.get("code", "")
                name = item.get("name", "")
            else:
                code = str(item)
                name = ""
            quantity = positions.get(code, {}).get("quantity", 0)
            latest_price = price_map.get(code, "")
            row = self.equity_table.rowCount()
            self.equity_table.insertRow(row)
            self.equity_table.setItem(row, 0, QTableWidgetItem(str(code)))
            self.equity_table.setItem(row, 1, QTableWidgetItem(str(name)))
            self.equity_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
            self.equity_table.setItem(row, 3, QTableWidgetItem(str(latest_price)))

        # 更新回报率曲线图
        account_id = account_data.get("name")
        if account_id:
            chart_path = SIM_TRADE_RESULT_DIR / f"{account_id}_return_curve.html"
            if chart_path.exists():
                self.return_rate_panel.show_file(str(chart_path))
            else:
                # 使用setHtml以避免找不到文件时的错误弹窗
                self.return_rate_panel.set_html("<html><body><p>未找到回报率曲线图，请运行一次完整的模拟交易。</p></body></html>")

    def _get_account_info_str(self, account_data):
        cash = account_data.get("cash", "")
        total_assets = account_data.get("total_assets", account_data.get("total_assets", ""))
        performance = account_data.get("performance", {})
        info_str = (
            f"现金余额: {cash}\n"
            f"总资产:   {total_assets}\n"
            f"总收益:   {performance.get('total_return', '')}\n"
            f"最大回撤  {performance.get('max_drawdown', '')}\n"
            f"总交易次数: {performance.get('total_trades', '')}\n"
            f"胜率:     {performance.get('win_rate', '')}\n"
            f"夏普比率: {performance.get('sharpe', '')}\n"
            f"开始日期: {account_data.get('start_time', '')}\n"
        )
        return info_str

    def _equity_box(self):
        """
        股票池+持仓box（兼容原wx实现，便于后续扩展右键菜单、双击等）
        """
        group = QGroupBox("股票池/持仓", self)
        vbox = QVBoxLayout(group)
        self.equity_table = QTableWidget(0, 4, group)
        self.equity_table.setHorizontalHeaderLabels(["代码", "名称", "持仓数量", "最新价"])
        vbox.addWidget(self.equity_table)
        group.setLayout(vbox)
        # 绑定双击事件 - 已通过itemSelectionChanged实现
        return group

    def on_equity_selection_changed(self):
        """
        选中股票池标的，加载对应K线图
        """
        selected_items = self.equity_table.selectedItems()
        if not selected_items:
            return
        row = selected_items[0].row()
        code_item = self.equity_table.item(row, 0)
        if not code_item:
            return
        code = code_item.text()
        if not code:
            return
        kline_path = SIM_TRADE_RESULT_DIR / f"{code}_kline_with_trades.html"
        print(f"尝试加载K线图: {kline_path}")
        if kline_path.exists():
            self.kline_panel.show_file(str(kline_path))
            self.web_tabs.setCurrentWidget(self.kline_panel)
        else:
            self.kline_panel.set_html(f"<html><body><p>未找到K线图文件: {kline_path}</p></body></html>")

    def _init_trade_engine(self):
        """
        直接通过cur_account获取账户信息，保证和选中账户一致
        """
        account_profile = self.account_manage.get_current_account() or {}
        account_name = account_profile.get("account_name") or account_profile.get("name") or ""
        # 检查equity_pool是否存在且非空
        equity_pool = account_profile.get("equity_pool", [])
        if not account_name or not account_profile or not equity_pool:
            QMessageBox.information(self, "提示", "请先选中一个有效账户，并确保已配置股票池")
            self.btn_run.setEnabled(True)
            self.btn_stop.setEnabled(False)
            return False
        self.trade_engine.before_trade(account_profile=account_profile)
        return True

    def on_select_account(self, account_id):
        """
        选中账户时在股票池中显示对应标的信息，并在账户信息窗口显示摘要信息
        """
        info = self.element_account.accounts.get(account_id, {})
        # 账户信息窗口只显示摘要
        if hasattr(self.element_account, "account_info_text"):
            self.element_account.account_info_text.setText(self._get_account_info_str(info))
        
        # 清空K线图并尝试加载回报率曲线
        self.kline_panel.set_html("")
        chart_path = SIM_TRADE_RESULT_DIR / f"{account_id}_return_curve.html"
        if chart_path.exists():
            self.return_rate_panel.show_file(str(chart_path))
        else:
            self.return_rate_panel.set_html("")

        self.equity_table.setRowCount(0)
        equity_pool = info.get("equity_pool", [])
        positions = info.get("positions", {})
        for item in equity_pool:
            if isinstance(item, dict):
                code = item.get("code", "")
                name = item.get("name", "")
            else:
                code = str(item)
                name = ""
            quantity = positions.get(code, {}).get("quantity", 0)
            # 查询最新价
            latest_price = ""
            try:
                if code and hasattr(self.db_manage, "realtime_db"):
                    df = self.db_manage.realtime_db.query_realtime(code=code)
                    if df is not None and not df.empty:
                        last_row = df.sort_values(by="date").iloc[-1]
                        latest_price = last_row.get("latest_price", "")
            except Exception as e:
                print(f"[DEBUG] 查询实时价格异常: {e}")
            row = self.equity_table.rowCount()
            self.equity_table.insertRow(row)
            self.equity_table.setItem(row, 0, QTableWidgetItem(str(code)))
            self.equity_table.setItem(row, 1, QTableWidgetItem(str(name)))
            self.equity_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
            self.equity_table.setItem(row, 3, QTableWidgetItem(str(latest_price)))
