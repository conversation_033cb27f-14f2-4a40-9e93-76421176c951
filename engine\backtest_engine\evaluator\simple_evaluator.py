from backtrader.feeds import PandasData
from backtrader import Cerebro
from backtest.evaluator.evaluator import BaseEvaluator

class SimpleEvaluator(BaseEvaluator):
    def __init__(self, plot = False, analyzer_cls=None):
        self.analyzer_cls = analyzer_cls
        self.plot = plot

    def evaluate(self, strategy_class, data, params, cash= 1000000):
        data_feed = PandasData(dataname=data)
        cerebro = Cerebro()
        cerebro.broker.setcash(cash)  # 设置初始资金
        print(f"Initial Cash: {cash}")  # 打印初始资金
        cerebro.adddata(data_feed)
        cerebro.addstrategy(strategy_class, **params)
        cerebro.broker.setcommission(self.commission)  # 设置手续费
        if self.analyzer_cls:
            cerebro.addanalyzer(self.analyzer_cls, _name=self.analyzer_cls.__name__)
        (sharp_ratio, last_order) = self._backtest(cerebro)
        final_value = cerebro.broker.getvalue()  # 获取最终资金
        print(f"Final Portfolio Value: {final_value}")  # 打印最终资金
        if(self.plot):
            cerebro.plot()
        return (sharp_ratio, last_order)

    def _backtest(self, cerebro):
        results = cerebro.run()
        score = results[0].analyzers.getbyname(self.analyzer_cls.__name__).get_analysis()
        last_signal = results[0].return_signal()
        return score, last_signal