import sys
from PyQt5.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QPushButton, QLabel, QMessageBox, QAction, QMenuBar, QToolBar, QComboBox
)
from PyQt5.QtCore import QTimer, QDateTime
from gui.panels.panel_backtest import PanelBacktest
from gui.panels.panel_trade import PanelTrade
from gui.panels.panel_factor_research import PanelFactorResearch

class MainFrame(QMainWindow):
    def __init__(self, *args, **kw):
        super().__init__()
        self.position_manager = kw.get("position_manager")
        self.account_manage = kw.get("account_manage")
        self.trade_engine = kw.get("trade_engine")
        self.db_manage = kw.get("db_manage")
        self.trade_ploter = kw.get("trade_ploter")
        self.cur_account_name = None

        self.setWindowTitle("TBOT")
        self.resize(1400, 900)
        self._init_menu_bar()
        self._init_tool_bar()
        self._init_statusbar()
        self._init_main_tabs()

    def _init_menu_bar(self):
        menu_bar = QMenuBar(self)
        tool_menu = menu_bar.addMenu("工具")
        snapshot_action = QAction("保存账户镜像", self)
        restore_action = QAction("恢复账户镜像", self)
        snapshot_action.triggered.connect(self.on_snapshot_account)
        restore_action.triggered.connect(self.on_restore_account)
        tool_menu.addAction(snapshot_action)
        tool_menu.addAction(restore_action)
        self.setMenuBar(menu_bar)

    def _init_tool_bar(self):
        tool_bar = QToolBar(self)
        snapshot_btn = QAction("保存镜像", self)
        restore_btn = QAction("恢复镜像", self)
        snapshot_btn.triggered.connect(self.on_snapshot_account)
        restore_btn.triggered.connect(self.on_restore_account)
        tool_bar.addAction(snapshot_btn)
        tool_bar.addAction(restore_btn)
        self.addToolBar(tool_bar)

    def _init_statusbar(self):
        self.statusBar().showMessage(QDateTime.currentDateTime().toString("yyyy-MM-dd HH:mm:ss"))
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_sys_time)
        self.timer.start(1000)

    def _update_sys_time(self):
        self.statusBar().showMessage(QDateTime.currentDateTime().toString("yyyy-MM-dd HH:mm:ss"))

    def _init_main_tabs(self):
        self.tabs = QTabWidget(self)
        self.setCentralWidget(self.tabs)
        self.tabs.addTab(
            PanelTrade(self.tabs,
                       position_manager=self.position_manager,
                       account_manage=self.account_manage,
                       trade_engine=self.trade_engine,
                       db_manage=self.db_manage),
            "Trade"
        )

        self.tabs.addTab(PanelFactorResearch(self.tabs, db_manage=self.db_manage), "Factor Research")
        self.tabs.addTab(PanelBacktest(self.tabs), "Backtest")
    def on_snapshot_account(self):
        account_id = getattr(self.account_manage, "_current_account_id", None)
        if not account_id:
            QMessageBox.information(self, "提示", "请先选择账户")
            return
        account_data = self.account_manage._load_account(account_id)
        if hasattr(self.db_manage, "snapshot_db"):
            self.db_manage.snapshot_db.insert_snapshot(
                account_id=account_id,
                date="init",
                value=account_data.get("total_assets", 0),
                extra_json=account_data
            )
        QMessageBox.information(self, "提示", "账户镜像已保存")

    def on_restore_account(self):
        account_id = getattr(self.account_manage, "_current_account_id", None)
        if not account_id:
            QMessageBox.information(self, "提示", "请先选择账户")
            return
        account_data = None
        if hasattr(self.db_manage, "snapshot_db"):
            df = self.db_manage.snapshot_db.query_snapshots(account_id, start_date="init", end_date="init")
            if df is not None and not df.empty:
                extra_json = df.iloc[0].get("extra_json", {})
                if isinstance(extra_json, str):
                    import json
                    try:
                        extra_json = json.loads(extra_json)
                    except Exception:
                        extra_json = {}
                account_data = extra_json
        if not account_data:
            QMessageBox.information(self, "提示", "未找到初始镜像，无法恢复")
            return
        self.account_manage._save_account(account_data)
        QMessageBox.information(self, "提示", "账户已恢复到初始镜像")
        print(f"[MainFrame] 恢复后账户信息: {account_data}")
        # 刷新账户信息界面（如有PanelTrade/PanelSimTrade等，需调用其刷新方法）
        for i in range(self.tabs.count()):
            page = self.tabs.widget(i)
            if hasattr(page, "trade_tabs"):
                for j in range(page.trade_tabs.count()):
                    sim_page = page.trade_tabs.widget(j)
                    if sim_page.__class__.__name__.lower() == "panelsimtrade":
                        if hasattr(sim_page, "_update_sim_trade_panel"):
                            sim_page._update_sim_trade_panel(account_data)

    def closeEvent(self, event):
        test_account_id = "test"
        if hasattr(self.db_manage, "snapshot_db"):
            self.db_manage.snapshot_db.clear_test_snapshots_except_init(test_account_id)
        if hasattr(self.account_manage, "clear_trade_records"):
            self.account_manage.clear_trade_records(test_account_id)
        event.accept()

# 启动入口（如需单独运行mainframe.py）
if __name__ == "__main__":
    app = QApplication(sys.argv)
    # 这里需要传递依赖的管理器和引擎实例
    # main_win = MainFrame(position_manager=..., account_manage=..., trade_engine=..., db_manage=..., trade_ploter=...)
    # main_win.show()
    # sys.exit(app.exec_())
    print("请在主程序中实例化MainFrame并传递依赖参数。")

