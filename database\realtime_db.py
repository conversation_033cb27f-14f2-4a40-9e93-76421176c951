from .quant_db import QuantDB
import pandas as pd

class RealTimeDB:
    REALTIME_COLUMNS = [
        "code", "name", "latest_price", "volumn", "amount", "date",
        "change", "open", "high", "low", "previous_close", "turnover_rate"
    ]
    REALTIME_DTYPES = {
        "code": "str",
        "name": "str",
        "latest_price": "float",
        "volumn": "float",
        "amount": "float",
        "date": "str",
        "change": "float",
        "open": "float",
        "high": "float",
        "low": "float",
        "previous_close": "float",
        "turnover_rate": "float"
    }

    def __init__(self, data_engine=None):
        self.db = QuantDB()
        self.data_engine = data_engine

    def init_realtime_tables(self):
        """
        初始化：创建 etf_realtime 和 stock_realtime 表（如不存在则创建，且下载一次实时数据作为初始数据）。
        使用duckdb SQL命令显式指定字段类型，避免类型推断错误。
        """
        dtype_map = {
            "str": "VARCHAR",
            "float": "DOUBLE"
        }
        for table, get_func in [
            ("stock_realtime", getattr(self.data_engine, "_get_stock_spot_info", None)),
            ("etf_realtime", getattr(self.data_engine, "_get_etf_spot_info", None))
        ]:
            if self.db.query(f"SELECT 1 FROM information_schema.tables WHERE table_name='{table}'").empty:
                columns = self.REALTIME_COLUMNS
                dtypes = self.REALTIME_DTYPES
                col_defs = ", ".join([f"{col} {dtype_map[dtypes[col]]}" for col in columns])
                create_sql = f"CREATE TABLE {table} ({col_defs})"
                self.db.conn.execute(create_sql)
                # 如果有data_engine和对应方法，下载一次实时数据
                if self.data_engine and callable(get_func):
                    info = get_func()
                    if info is not None and not info.empty:
                        info = self.data_engine._convert_name_em(info)
                        info["date"] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                        for col in ["volumn", "amount"]:
                            if col not in info.columns:
                                info[col] = None
                        df = info.reindex(columns=columns)
                        for col, typ in dtypes.items():
                            if col in df.columns:
                                if typ == "str":
                                    df[col] = df[col].astype(str)
                                elif typ == "float":
                                    df[col] = pd.to_numeric(df[col], errors="coerce")
                        self.db.insert_df(table, df)

    def _refresh_single_realtime(self, get_func, table_name):
        """
        通用刷新方法，get_func为data_engine的方法，table_name为目标表名。
        如果MERGE不支持，则用delete+insert实现upsert。
        插入前强制所有字段类型，避免duckdb类型推断错误。
        """
        info = get_func()
        if info is not None and not info.empty:
            info = self.data_engine._convert_name_em(info)
            info["date"] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
            for col in ["volumn", "amount"]:
                if col not in info.columns:
                    info[col] = None
            cols = self.REALTIME_COLUMNS
            dtypes = self.REALTIME_DTYPES
            table_info = self.db.query(f"PRAGMA table_info('{table_name}')")
            actual_cols = [c for c in table_info['name'].tolist() if c in info.columns]
            df = info.reindex(columns=actual_cols)
            for col, typ in dtypes.items():
                if col in df.columns:
                    if typ == "str":
                        df[col] = df[col].astype(str)
                    elif typ == "float":
                        df[col] = pd.to_numeric(df[col], errors="coerce")
            try:
                for _, row in df.iterrows():
                    code = row["code"]
                    self.db.conn.execute(f"DELETE FROM {table_name} WHERE code='{code}'")
                    self.db.insert_df(table_name, pd.DataFrame([row[actual_cols]]))
            except Exception as e:
                print(f"[RealTimeDB] upsert fallback异常: {e}")

    def refresh_realtime(self):
        """
        刷新实时行情表（不需要外部传参，直接用self.data_engine）
        """
        self._refresh_single_realtime(self.data_engine._get_stock_spot_info, "stock_realtime")
        self._refresh_single_realtime(self.data_engine._get_etf_spot_info, "etf_realtime")

    def query_realtime(self, code=None, table=None):
        """
        查询实时表（stock_realtime 或 etf_realtime），可按code筛选。
        :param code: 若指定则自动判断stock/etf分类
        :param table: 指定表名（优先级最高）
        :return: DataFrame
        """
        # 优先使用table参数
        if table:
            where = f"code='{code}'" if code else None
            return self.db.read_table(table, where=where)
        # 分类规则：ETF代码以5/1/15/16/51开头
        if code:
            etf_prefixes = ("5", "1", "15", "16", "51")
            if code.startswith(etf_prefixes):
                table = "etf_realtime"
            else:
                table = "stock_realtime"
            where = f"code='{code}'"
        else:
            # 默认返回全部股票实时表
            table = "stock_realtime"
            where = None
        return self.db.read_table(table, where=where)

    def get_latest_data_map(self, equity_pool):
        """
        获取股票池所有标的的最新bar数据，返回{symbol: latest_row}，兼容latest_price/close
        """
        latest_data = {}
        price_map = {}
        for item in equity_pool:
            code = item["code"] if isinstance(item, dict) else str(item)
            df = self.query_realtime(code=code)
            if df is not None and not df.empty:
                # 兼容：如果有latest_price列，则重命名为close
                if "latest_price" in df.columns and "close" not in df.columns:
                    df = df.rename(columns={"latest_price": "close"})
                latest_row = df.iloc[-1]
                latest_data[code] = latest_row
                price_map[code] = latest_row["close"]
        return latest_data, price_map

    def get_all_realtime_tables(self):
        """
        返回所有以_realtime结尾的实时数据表名
        """
        tables = []
        try:
            df = self.db.query("SELECT table_name FROM information_schema.tables")
            if df is not None and not df.empty:
                tables = [t for t in df["table_name"].tolist() if t.endswith("_realtime")]
        except Exception as e:
            print(f"[RealTimeDB] 获取实时表名异常: {e}")
        return tables
