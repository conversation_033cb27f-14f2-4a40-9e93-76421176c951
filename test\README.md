# 测试目录

本目录包含了项目的所有测试文件，按功能分类组织。

## 目录结构

```
test/
├── README.md                           # 本文件
├── run_tests.py                        # 测试运行器（批量运行）
├── quick_test.py                       # 快速测试脚本
├── debug_factor_index.py              # 因子索引结构调试工具
├── test_factor_analysis_complete.py   # 完整因子分析流程测试
├── test_factor_panel.py               # 因子面板功能测试
├── test_factor_panel_ui.py            # 因子面板UI测试
├── verify_factor_panel.py             # 因子面板验证测试
├── simple_test.py                     # 简单功能测试
├── standalone_test.py                 # 独立测试（不依赖外部库）
├── test_framework.py                  # 框架功能测试
└── gui_factor_research_demo.py        # GUI因子研究演示
```

## 测试文件说明

### 测试运行器

#### `run_tests.py`
- **用途**: 批量测试运行器
- **功能**: 自动运行所有测试，生成详细报告
- **特点**: 支持超时控制、错误捕获、分类测试
- **运行**: `python test/run_tests.py`

#### `quick_test.py`
- **用途**: 快速验证脚本
- **功能**: 运行最基本的测试，快速检查框架状态
- **特点**: 轻量级，5个核心测试，适合日常验证
- **运行**: `python test/quick_test.py`

### 核心功能测试

#### `test_factor_analysis_complete.py`
- **用途**: 完整的因子分析流程测试
- **功能**: 测试动量因子、RSI因子、价量趋势因子的计算和分析
- **特点**: 包含数据结构验证和错误处理
- **运行**: `python test/test_factor_analysis_complete.py`

#### `simple_test.py`
- **用途**: 基础功能快速验证
- **功能**: 测试模块导入、因子创建、配置等基本功能
- **特点**: 轻量级，适合快速检查框架状态
- **运行**: `python test/simple_test.py`

#### `test_framework.py`
- **用途**: 框架核心功能测试
- **功能**: 测试因子计算、分析、批量处理等高级功能
- **特点**: 全面的功能覆盖
- **运行**: `python test/test_framework.py`

#### `standalone_test.py`
- **用途**: 独立测试（不依赖alphalens）
- **功能**: 测试框架在没有可选依赖时的工作状态
- **特点**: 验证简化分析方法的正确性
- **运行**: `python test/standalone_test.py`

### GUI测试

#### `test_factor_panel_ui.py`
- **用途**: 因子研究面板UI测试
- **功能**: 测试优化后的界面布局和交互
- **特点**: 可视化界面测试，需要GUI环境
- **运行**: `python test/test_factor_panel_ui.py`

#### `test_factor_panel.py`
- **用途**: 因子面板功能测试
- **功能**: 测试面板的业务逻辑和数据处理
- **特点**: 专注于功能而非界面
- **运行**: `python test/test_factor_panel.py`

#### `verify_factor_panel.py`
- **用途**: 因子面板验证测试
- **功能**: 验证面板各项功能的正确性
- **特点**: 详细的验证步骤和错误报告
- **运行**: `python test/verify_factor_panel.py`

#### `gui_factor_research_demo.py`
- **用途**: GUI因子研究演示
- **功能**: 展示GUI界面的完整功能
- **特点**: 演示性质，适合功能展示
- **运行**: `python test/gui_factor_research_demo.py`

### 调试工具

#### `debug_factor_index.py`
- **用途**: 因子数据索引结构调试
- **功能**: 诊断MultiIndex相关问题
- **特点**: 详细的数据结构检查和错误定位
- **运行**: `python test/debug_factor_index.py`

## 运行测试

### 推荐方式
```bash
# 快速验证（推荐日常使用）
python test/quick_test.py

# 批量运行所有测试
python test/run_tests.py
```

### 单独运行
```bash
# 基础功能测试
python test/simple_test.py

# 完整功能测试
python test/test_factor_analysis_complete.py
```

### GUI测试
```bash
# UI界面测试
python test/test_factor_panel_ui.py

# 功能验证测试
python test/verify_factor_panel.py
```

### 调试工具
```bash
# 索引结构调试
python test/debug_factor_index.py
```

## 测试环境要求

- Python 3.7+
- pandas, numpy
- PyQt5 (GUI测试)
- factor_research框架
- 可选: alphalens (增强分析功能)

## 注意事项

1. **路径问题**: 所有测试文件已更新路径引用，适配test目录结构
2. **依赖管理**: 测试会自动检测可选依赖，优雅降级
3. **数据库**: 某些测试需要数据库连接，会自动使用模拟数据
4. **GUI环境**: GUI测试需要图形界面环境

## 测试覆盖

- ✅ 因子计算和注册
- ✅ 数据适配和处理
- ✅ 因子分析（含/不含alphalens）
- ✅ GUI界面和交互
- ✅ 错误处理和边界情况
- ✅ 配置管理
- ✅ 批量处理
- ✅ 报告生成

## 贡献指南

添加新测试时请：
1. 遵循现有命名规范
2. 添加详细的文档说明
3. 更新本README文件
4. 确保测试的独立性和可重复性
