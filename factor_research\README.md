# 因子研究框架

基于alphalens的单因子研究框架，提供完整的因子计算、分析和可视化功能。

## 主要特性

- **因子计算**: 支持多种技术指标和量价因子
- **因子分析**: 基于alphalens的专业因子分析，支持可选依赖模式
- **可视化**: 丰富的图表和报告生成
- **扩展性**: 易于添加新的因子类型
- **配置化**: 灵活的配置管理系统
- **可靠性**: 完善的错误处理和测试覆盖

## 快速开始

```python
from factor_research import FactorResearch
from factor_research.factors.momentum import MomentumFactor

# 初始化研究框架
research = FactorResearch()

# 注册因子
research.register_factor(MomentumFactor, lookback_period=20)

# 计算因子
factor_data = research.calculate_factor(
    'MomentumFactor',
    assets=['000001.SZ', '000002.SZ'],
    start_date='2023-01-01',
    end_date='2023-12-31'
)

# 分析因子
results = research.analyze_factor(factor_data)

# 生成报告
research.generate_report(results, 'factor_analysis.html')
```

## 安装依赖

### 必需依赖
```bash
pip install pandas numpy matplotlib seaborn PyYAML
```

### 可选依赖（推荐）
```bash
pip install alphalens  # 提供完整的因子分析功能
```

## 目录结构

```
factor_research/
├── core/                   # 核心模块
│   ├── factor_research.py  # 主入口类
│   ├── factor_engine.py    # 因子计算引擎
│   ├── analyzer.py         # 因子分析器
│   ├── data_adapter.py     # 数据适配器
│   └── visualizer.py       # 可视化组件
├── factors/                # 因子库
│   ├── base.py            # 基础因子类
│   └── momentum.py        # 动量类因子
├── utils/                 # 工具模块
│   ├── config.py          # 配置管理
│   └── exceptions.py      # 异常定义
├── doc/                   # 文档目录
│   ├── README.md          # 文档索引
│   ├── USER_GUIDE.md      # 用户指南
│   ├── API_DESIGN.md      # API设计
│   ├── FACTOR_LIBRARY.md  # 因子库文档
│   ├── QUICK_START.md     # 快速开始
│   └── ...               # 其他文档
└── examples/              # 使用示例
```

## 📚 文档

完整文档请参考 **[doc/](doc/)** 目录：

- **[文档索引](doc/README.md)** - 所有文档的导航
- **[快速开始](doc/QUICK_START.md)** - 5分钟快速体验
- **[用户指南](doc/USER_GUIDE.md)** - 详细使用说明
- **[API设计](doc/API_DESIGN.md)** - 接口文档
- **[因子库文档](doc/FACTOR_LIBRARY.md)** - 因子扩展指南
- **[实现总结](doc/IMPLEMENTATION_SUMMARY.md)** - 项目完整总结

## 🧪 测试

运行测试验证框架功能：

```bash
cd examples
python simple_test.py
```

## 📄 许可证

MIT License
