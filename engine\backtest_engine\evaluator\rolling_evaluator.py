import numpy as np
from backtrader.feeds import PandasData
from backtrader import Cerebro
from backtest.evaluator.evaluator import BaseEvaluator

class RollingEvaluator(BaseEvaluator):
    def __init__(self, step_size=21, window_size=252, analyzer_cls = None):
        """
        初始化滚动评估器，指定窗口大小
        :param window_size: 每个滚动窗口的大小
        """
        self.window_size = window_size
        self.step_size = step_size
        self.analyzer_cls = analyzer_cls

    def evaluate(self, strategy_class, data, params):
        """
        执行滚动回测评估
        :param strategy_class: 策略类
        :param data: 回测数据
        :param params: 需要优化的参数
        :return: 滚动回测的平均得分
        """
        data_len = len(data)
        start_idx = 0
        end_idx = self.window_size


        scores = []  # 用于存储每个窗口的回测结果
        while end_idx <= data_len:
            # 对每个滚动窗口进行回测
            window_data = data[start_idx:end_idx]
            data_feed = PandasData(dataname=window_data)
            cerebro = Cerebro()
            cerebro.adddata(data_feed)  # 只使用当前滚动窗口的数据
            cerebro.addstrategy(strategy_class, **params)
            cerebro.broker.setcommission(self.commission)  # 设置手续费

            if self.analyzer_cls:
                cerebro.addanalyzer(self.analyzer_cls, _name=self.analyzer_cls.__name__)

            # 执行回测
            score = self._backtest(cerebro)
            scores.append(score)

            # 滚动窗口
            start_idx += self.step_size
            end_idx += self.step_size
            if end_idx > data_len:
                break

        # 最终返回窗口中所有回测的平均分数
        return np.mean(scores)

    def _backtest(self, cerebro):
        """回测并返回绩效指标"""
        results = cerebro.run()
        score = results[0].analyzers.getbyname(self.analyzer_cls.__name__).get_analysis()
        return score

