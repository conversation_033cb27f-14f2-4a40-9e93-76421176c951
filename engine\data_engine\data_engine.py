import akshare as ak
import pandas as pd

class DataEngine(object):
    ETF_PREFIXES = ("5", "1", "15", "16", "51")

    def __init__(self, quant_db=None):
        self.quant_db = quant_db

    def _convert_name_em(self, df) -> pd.DataFrame:
        df_new = df.rename(columns={
            '序号': 'index',
            '代码': 'code',
            '名称': 'name',
            '最新价': 'latest_price',
            '今开': 'open',
            '昨收': 'previous_close',
            '量比': 'volume_ratio',
            '日期': 'date',
            '数据日期': 'date',
            '更新时间': 'update_time',
            '开盘': 'open',
            '开盘价': 'open',
            '最高价': 'high',
            '最高': 'high',
            '最低价': 'low',
            '最低': 'low',
            '收盘': 'close',
            '涨跌幅': 'change',
            '涨跌额': 'change_amount',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '换手率': 'turnover_rate',
            '市盈率-动态': 'pe_dynamic',
            '市净率': 'pb',
            '市盈率-静态': 'pe_static',
            '总市值': 'market_cap',
            '涨速': 'speed',
            
        })
        return df_new

    def _get_stock_spot_info(self):
        stock_info = ak.stock_zh_a_spot_em()
        return stock_info
    
    def _get_etf_spot_info(self):
        etf_info = ak.fund_etf_spot_em()
        print("获取ETF现价 {etf_info.shape[0]} 条数据")
        return etf_info

    @staticmethod
    def is_etf_code(code):
        return code.startswith(DataEngine.ETF_PREFIXES)

    def _get_stock_spot_info_by_code(self, code):
        # 使用统一ETF判断
        if self.is_etf_code(code):
            # 取ETF行情
            df = ak.fund_etf_spot_em()
        else:
            # 取股票行情
            df = ak.stock_zh_a_spot_em()
        
        row = df[df['代码'] == code]
        if not row.empty:
            name = row.iloc[0]['名称']
            price = row.iloc[0]['最新价']
            return {'name': name, 'price': price}
        return {'name': '', 'price': ''}

    def _get_stock_spot_info_pool(self, stock_pool):
        """
        获取股票池(stock_pool为代码列表)的现价和名称，返回列表[dict]，每个dict为该标的的完整一行数据（akshare原始数据，dict格式）。
        """
        stock_info = ak.stock_zh_a_spot_em()
        etf_info = ak.fund_etf_spot_em()
        if stock_info is None or etf_info is None:
            print("akshare返回None，无法获取股票或ETF现价数据")
            return []
        result = []
        for code in stock_pool:
            if self.is_etf_code(code):
                df = etf_info
            else:
                df = stock_info
            
            df = self._convert_name_em(df)
            row = df[df['code'] == code]            
            if not row.empty:
                # 返回该行所有字段的dict
                result.append(row.iloc[0].to_dict())
            else:
                result.append({'code': code})  # 只返回代码，其他字段缺失
            print(f"获取股票数据: {code}, 现价: {row['latest_price'].values[0] if not row.empty else 'N/A'}")
        return result

    def _get_stock_hist_info(self, symbol, period, adjust, file_path=None):    
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if(symbol.startswith("H")):
                    symbol = symbol[1:]
                    df = ak.stock_hk_hist(symbol, period, adjust)
                else:
                    if self.is_etf_code(symbol):
                        df = ak.fund_etf_hist_em(symbol, period, adjust)
                    else:
                        df = ak.stock_zh_a_hist(symbol, period, adjust)
                df = self._convert_name_em(df)
                if file_path != None:
                    df.to_csv(file_path, index=False, encoding='utf_8_sig')
                return df
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"获取股票数据失败: {symbol}, 尝试重试({attempt+1}/{max_retries})，错误信息: {str(e)}")
                    import time
                    time.sleep(2)
                    continue
                else:
                    print(f"获取股票数据失败: {symbol}, 已重试{max_retries}次")
                    raise ValueError(f"获取股票数据失败: {str(e)}") from e

    def _get_option_hist_info(self, symbol):
        pass

    def _get_option_hist_info_for_year(self, year):
        pass


    def _convert_spot_to_hist_em(self, spot_df):
        """
        将现货数据转换为历史数据格式，返回DataFrame。
        """
        spot_df = self._convert_name_em(spot_df)
        # 只保留需要的列
        columns = ['date', 'open', 'latest_price', 'high', 'low', 'volume', 'amount', 'amplitude', 'change', 'change_amount', 'turnover_rate']
        hist_df = spot_df[columns].copy()
        hist_df['date'] = pd.to_datetime(hist_df['date'])  # 保持为datetime类型
        hist_df.rename(columns={'latest_price': 'close'}, inplace=True)
        hist_df = hist_df.set_index('date')  # 设置index为date
        return hist_df

    def _replace_last_bar(self, datafeed, latest_bar):
        """
        替换数据源的最后一行数据，latest_bar为akshare最新行情dict，需按历史数据格式写入。
        """
        # 利用_convert_spot_to_hist_em生成标准格式DataFrame
        spot_df = pd.DataFrame([latest_bar])
        hist_df = self._convert_spot_to_hist_em(spot_df)
        if hasattr(datafeed, 'p') and hasattr(datafeed.p, 'dataname'):
            df = datafeed.p.dataname
            print(df.iloc[-1])
            if hasattr(df, 'iloc') and len(df) > 0 and len(hist_df) > 0:
                # 对齐列名，确保赋值不会出错
                hist_row = hist_df.iloc[0]
                hist_row = hist_row.reindex(df.columns)  # 按df的列顺序对齐
                df.loc[df.index[-1], :] = hist_row.values
                # 确保 index 还是 DatetimeIndex
                if not isinstance(df.index, pd.DatetimeIndex):
                    df.index = pd.to_datetime(df.index)
                datafeed.p.dataname = df  # 更新回去
                print(f"已替换最后一行为: {hist_row.to_dict()}")
            else:
                print("数据源无历史数据，无法替换")
        else:
            print("数据源类型不支持直接替换最后一行")

    def _append_last_bar(self, datafeed, latest_bar):
        """
        向数据源追加最新一行数据，latest_bar为akshare最新行情dict，需按历史数据格式写入。
        """
        spot_df = pd.DataFrame([latest_bar])
        hist_df = self._convert_spot_to_hist_em(spot_df)
        if hasattr(datafeed, 'p') and hasattr(datafeed.p, 'dataname'):
            df = datafeed.p.dataname
            if hasattr(df, 'append') and len(hist_df) > 0:
                df = df.append(hist_df.iloc[0], ignore_index=True)
                datafeed.p.dataname = df  # 更新回去
                print(f"已追加新行: {hist_df.iloc[0].to_dict()}")
            else:
                print("数据源不支持追加操作")
        else:
            print("数据源类型不支持追加操作")

    @staticmethod
    def get_category_by_code(code):
        """
        根据标的代码判断数据库category（'stock' 或 'etf'）
        """
        return "etf" if DataEngine.is_etf_code(code) else "stock"
