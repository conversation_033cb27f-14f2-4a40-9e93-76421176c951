class SelectionStrategy:
    """
    黄金配置策略
    """

    def __init__(self):
        pass

    def select(self, data):
        """
        选择黄金ETF进行配置
        :param data: 输入数据
        :return: 配置结果
        """
        candidates = [
            {
                "code": "518880",          # 黄金ETF代码
                "name": "黄金ETF",
                "alloction_ratio": 1  # 占总仓位20%
            }
        ]
        
        # 这里可以添加更多的逻辑来选择合适的黄金ETF
        return candidates