import backtrader as bt

class WinRateAnalyzer(bt.Analyzer):
    """
    Custom Backtrader Analyzer to calculate winrate based on predictions.
    """
    def __init__(self):
        # Store predictions with their entry price and timestamp
        self.predictions = []
        # Successful predictions count
        self.success_count = 0
        # Total predictions count
        self.total_count = 0
        # Time window to evaluate predictions
        self.time_window = 10  # Number of bars after the prediction

    def notify_signal(self, signal_price):
        """
        Record a prediction when a signal is received.
        :param signal_price: The price at which the signal is generated.
        """
        self.predictions.append({
            'price': signal_price,
            'timestamp': len(self.datas[0]),  # Current bar index
        })

    def next(self):
        """
        Evaluate predictions within the time window.
        """
        current_bar = len(self.datas[0])  # Current bar index
        current_close = self.datas[0].close[0]  # Current close price

        # Check predictions that fall within the time window
        for prediction in self.predictions[:]:
            if current_bar - prediction['timestamp'] <= self.time_window:
                # Evaluate prediction success
                if current_close > prediction['price']:
                    self.success_count += 1
                self.total_count += 1
                # Remove evaluated prediction
                self.predictions.remove(prediction)

    def get_analysis(self):
        """
        Return the winrate analysis.
        """
        winrate = (self.success_count / self.total_count) * 100 if self.total_count > 0 else 0
        return {
            'winrate': winrate,
            'success_count': self.success_count,
            'total_count': self.total_count,
        }
