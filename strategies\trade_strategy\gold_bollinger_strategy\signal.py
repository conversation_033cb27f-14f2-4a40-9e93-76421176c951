import pandas as pd
from common.config import SIM_TRADE_RESULT_DIR
from database.hist_db import HistDB
from common.config import PERIOD_MAP
from common.signal_const import SignalStrength
import csv
import os
class StrategySignal:
    params = (
        ('period', 15),  # 布林带周期
        ('dev', 2),      # 布林带偏差
        ('vol_ma_period', 5) # 成交量移动平均周期
    )

    params_bound = {
        'period': (10, 30),
        'dev': (1, 5),
        'vol_ma_period': (3, 20)
    }

    def __init__(self, period=15, dev=2, vol_ma_period=5):
        # 去掉 super().__init__ 调用
        self.period = period
        self.dev = dev
        self.vol_ma_period = vol_ma_period

    def debug_signal(self, signal, boll_upper, boll_middle, boll_lower, vol_ma, close, volume, date=None):
        """
        将调试信息写入CSV文件，增加日期字段，保存到SIM_TRADE_RESULT_DIR目录下
        """
        import os
        log_path = os.path.join(SIM_TRADE_RESULT_DIR, "signal_debug.csv")
        file_exists = os.path.isfile(log_path)
        with open(log_path, "a", encoding="utf-8", newline='') as f:
            import csv
            writer = csv.writer(f)
            if not file_exists:
                writer.writerow([
                    "date", "signal", "boll_upper", "boll_middle", "boll_lower", "vol_ma", "close", "volume"
                ])
            writer.writerow([
                date, signal, boll_upper, boll_middle, boll_lower, vol_ma, close, volume
            ])

    def get_signal(self, data, debug=False):
        """
        生成交易信号，data为当前bar（dict或Series）
        """
        # 简洁实现
        p, d, v = self.period, self.dev, self.vol_ma_period
        close = data.get("close")
        volume = data.get("volume", 0)
        boll_upper = data.get(f"boll_upper_{p}_{d}")
        boll_middle = data.get(f"boll_middle_{p}_{d}")
        boll_lower = data.get(f"boll_lower_{p}_{d}")
        vol_ma = data.get(f"vol_ma_{v}")

        signal = SignalStrength.NONE
        if boll_middle is not None and close < boll_middle:
            signal = SignalStrength.WEAK_BUY
        elif boll_lower is not None and close < boll_lower:
            signal = SignalStrength.STRONG_BUY
        elif (
            boll_upper is not None and close > (boll_upper * 0.95) and
            vol_ma is not None and volume > vol_ma
        ):
            signal = SignalStrength.WEAK_SELL

        if debug:
            date = data.get("date", None)
            self.debug_signal(signal, boll_upper, boll_middle, boll_lower, vol_ma, close, volume, date)
        return signal

