# 建议的目录结构和组织方式如下：

"""
e:\trade\t_trade\strategies\trade_strategy\GoldBollinger\
    __init__.py
    signal.py         # 只负责信号/指标逻辑（如GoldBollingerSignal类，基于pandas/talib）
    backtest.py       # 回测策略（如GoldBollingerBacktestStrategy，继承bt.Strategy）
    trade.py          # 交易/模拟盘策略（如GoldBollingerTradeStrategy，供TradeEngine调用）
    config.py         # 策略参数、调优范围等（可选）
    utils.py          # 该策略专用的工具函数（可选）

这样每个策略的信号、回测、交易实现都集中在一个文件夹下，便于维护和扩展。
主引擎可通过 import strategies.trade_strategy.GoldBollinger.signal 等方式灵活调用。
"""
