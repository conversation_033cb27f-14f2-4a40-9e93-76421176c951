# logger_config.py
import logging
import logging.handlers
import os

# --- 配置 ---
# 日志文件存放目录在这个文件的上一层目录
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_DIR = os.path.join(parent_dir, 'log')  # 日志文件存放目录
print(f"日志目录: {LOG_DIR}")
LOG_FILENAME_BASE = 'quant'  # 日志文件名前缀 (最终文件名会是 app_YYYY-MM-DD.log)
# 设置日志记录的最低级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL = logging.DEBUG
LOG_FORMAT = '[%(asctime)s][%(levelname)s] %(message)s'  # 日志格式
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'  # 日期时间格式 (年-月-日 小时:分钟:秒)
FILE_DATE_FORMAT = '%Y-%m-%d'  # 日志文件名中的日期格式
# -----------


def setup_logger():
    """配置并返回一个全局 logger"""
    # --- 确保日志目录存在 ---
    if not os.path.exists(LOG_DIR):
        try:
            os.makedirs(LOG_DIR)
            print(f"日志目录 '{LOG_DIR}' 已创建。")
        except OSError as e:
            print(f"创建日志目录 '{LOG_DIR}' 失败: {e}")
            return None

    # --- 创建 Logger ---
    # 使用一个固定的名称获取 logger，确保全局唯一性
    logger_instance = logging.getLogger('global_logger')
    logger_instance.setLevel(LOG_LEVEL)  # 设置 logger 的级别

    # --- 防止重复添加 Handler ---
    # 如果 logger 已经有 handlers，则不再添加，避免日志重复记录
    if logger_instance.hasHandlers():
        print(f"Logger '{logger_instance.name}' 已存在，跳过重复添加 Handler。")
        return logger_instance

    # --- 配置 Formatter ---
    formatter = logging.Formatter(LOG_FORMAT, datefmt=DATE_FORMAT)

    # --- 配置 File Handler (按天轮换) ---
    log_file_path = os.path.join(LOG_DIR, f"{LOG_FILENAME_BASE}.log")

    # 使用 TimedRotatingFileHandler 实现按天轮换
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_file_path,
        when='midnight',    # 在每天午夜进行日志轮换
        interval=1,         # 轮换间隔为1天
        backupCount=5,     # 保留最近5天的日志文件
        encoding='utf-8',
        delay=True         # 立即打开日志文件
    )
    
    # 设置日志文件后缀格式为 .YYYY-MM-DD
    file_handler.suffix = "%Y-%m-%d"
    
    # 重要：告诉处理器使用时间元组的 strftime 格式化后缀
    file_handler.extMatch = r"^\d{4}-\d{2}-\d{2}$"
    
    file_handler.setFormatter(formatter)
    file_handler.setLevel(LOG_LEVEL)

    # --- 配置 Console Handler (用于在控制台输出日志) ---
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)  # 控制台显示 INFO 及以上级别

    # --- 将 Handlers 添加到 Logger ---
    logger_instance.addHandler(file_handler)
    logger_instance.addHandler(console_handler)

    # --- 防止日志传播到 root logger ---
    logger_instance.propagate = False

    print(f"Logger '{logger_instance.name}' setup complete. Logging to directory: '{LOG_DIR}'")
    return logger_instance


# --- 创建并导出全局 logger 实例 ---
# 当这个模块被导入时，setup_logger() 会被调用一次，创建 logger 实例
logger = setup_logger()

# --- 如果需要在 setup_logger 失败时处理 (例如目录创建失败) ---
if logger is None:
    print("Logger setup failed. Exiting.")
    exit(1)

if __name__ == "__main__":
    print("Logger dir:", LOG_DIR)
    # 测试 logger
    logger.debug("Debug message")
    logger.info("Info message")
    logger.warning("Warning message")
    logger.error("Error message")
    logger.critical("Critical message")
