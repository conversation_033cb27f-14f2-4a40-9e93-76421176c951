from gui.panels.panel_real_trade import PanelRealTrade
from gui.panels.panel_sim_trade import PanelSimTrade
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTabWidget

class PanelTrade(QWidget):
    def __init__(self, parent=None, *args, **kw):
        super().__init__(parent)
        self.position_manager = kw.get("position_manager")
        self.account_manage = kw.get("account_manage")
        self.trade_engine = kw.get("trade_engine")
        self.db_manage = kw.get("db_manage")
        self._init_ui()

    def _init_ui(self):
        vbox = QVBoxLayout()
        self.setLayout(vbox)

        self.trade_tabs = QTabWidget(self)
        self.trade_tabs.addTab(
            PanelSimTrade(self.trade_tabs,
                          position_manager=self.position_manager,
                          account_manage=self.account_manage,
                          trade_engine=self.trade_engine,
                          db_manage=self.db_manage),
            "simulated trade"
        )
        self.trade_tabs.addTab(PanelRealTrade(self.trade_tabs), "real trade")
        self.trade_tabs.setCurrentIndex(0)
        vbox.addWidget(self.trade_tabs)

    def _show_panel(self, panel_class):
        # 在QTabWidget中切换到指定类型的面板
        for idx in range(self.trade_tabs.count()):
            page = self.trade_tabs.widget(idx)
            if isinstance(page, panel_class):
                self.trade_tabs.setCurrentIndex(idx)
                break