# T_TRADE

个人量化交易分析工具

## 主要模块

- `gui/`  
  图形界面，主窗口在 `gui.mainframe.MainFrame`。

- `database/`  
  数据库管理，负责历史数据、实时数据、快照等的存储与更新。入口为 `database.db_manage.DBManage`。

- `portfolio/account/`  
  账户管理，负责账户信息、股票池等，入口为 `position.account.account_manage.AccountManage`。

- `engine/`  
  核心引擎模块，包含：
  - `data_engine/` 数据引擎，负责行情数据的获取与处理。
  - `trade_engine/` 交易引擎，负责策略执行、信号生成与交易管理。
  - `mail_engine/` 邮件引擎，负责通知、预警等邮件发送功能。
  - `backtest_engine/` 回测引擎，负责策略回测与绩效分析。
  - 其他扩展引擎可按需添加。

- `plot/`  
  交易可视化，入口为 `plot.trade_plot.TradePlot`。

## 启动方式

```bash
python main.py
```

## 数据库说明

详见 `database/README.md`，包括数据库结构、初始化、实时与历史数据更新机制等。

## 主要功能

- 多账户管理与股票池配置
- 历史与实时行情数据自动更新
- 支持多策略回测与实盘信号生成
- 图形化界面展示账户、行情与交易信号

## 依赖

- wxPython
- backtrader
- talib
- 其他详见 requirements.txt

