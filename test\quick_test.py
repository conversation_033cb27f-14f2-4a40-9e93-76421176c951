#!/usr/bin/env python3
"""
快速测试脚本

运行最基本的测试，快速验证框架状态。
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_basic_imports():
    """测试基本导入"""
    print("1. 测试基本导入...")
    
    try:
        from factor_research import FactorResearch
        from factor_research.factors.momentum import MomentumFactor, RSIFactor, PriceVolumeTrendFactor
        from factor_research.core.analyzer import FactorAnalyzer
        from factor_research.utils.config import FactorConfig
        print("   ✅ 基本模块导入成功")
        return True
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def test_factor_creation():
    """测试因子创建"""
    print("2. 测试因子创建...")
    
    try:
        from factor_research.factors.momentum import MomentumFactor, RSIFactor, PriceVolumeTrendFactor
        
        # 创建因子实例
        momentum = MomentumFactor(lookback_period=20, skip_period=1)
        rsi = RSIFactor(period=14)
        pvt = PriceVolumeTrendFactor(period=20)
        
        print(f"   ✅ 因子创建成功: {momentum.name}, {rsi.name}, {pvt.name}")
        return True
    except Exception as e:
        print(f"   ❌ 因子创建失败: {e}")
        return False

def test_framework_init():
    """测试框架初始化"""
    print("3. 测试框架初始化...")
    
    try:
        from factor_research import FactorResearch
        
        # 初始化框架
        research = FactorResearch()
        
        # 检查注册的因子
        factors = research.list_factors()
        print(f"   ✅ 框架初始化成功，已注册因子: {factors}")
        return True
    except Exception as e:
        print(f"   ❌ 框架初始化失败: {e}")
        return False

def test_gui_imports():
    """测试GUI导入"""
    print("4. 测试GUI导入...")
    
    try:
        from gui.panels.panel_factor_research import PanelFactorResearch
        print("   ✅ GUI面板导入成功")
        return True
    except Exception as e:
        print(f"   ❌ GUI导入失败: {e}")
        return False

def test_mock_data():
    """测试模拟数据生成"""
    print("5. 测试模拟数据生成...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建模拟数据
        dates = pd.date_range(start='2023-01-01', end='2023-01-31', freq='D')
        assets = ['000001', '000002', '600000']
        
        # 创建MultiIndex
        index = pd.MultiIndex.from_product([dates, assets], names=['date', 'asset'])
        
        # 生成随机因子值
        np.random.seed(42)
        factor_values = np.random.normal(0, 1, len(index))
        
        # 创建因子数据Series
        factor_data = pd.Series(factor_values, index=index, name='test_factor')
        
        print(f"   ✅ 模拟数据生成成功: {len(factor_data)} 条记录")
        return True
    except Exception as e:
        print(f"   ❌ 模拟数据生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速测试 - 因子研究框架")
    print("="*50)
    
    tests = [
        test_basic_imports,
        test_factor_creation,
        test_framework_init,
        test_gui_imports,
        test_mock_data,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   💥 测试异常: {e}")
    
    print("\n" + "="*50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有快速测试都通过了！")
        print("💡 可以运行 'python test/run_tests.py' 进行完整测试")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败")
        print("🔧 请检查环境配置和依赖安装")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
