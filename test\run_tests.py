#!/usr/bin/env python3
"""
测试运行器

批量运行项目中的各种测试，提供统一的测试入口。
"""

import sys
import os
import subprocess
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_test(test_file, description, timeout=60):
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"运行测试: {description}")
    print(f"文件: {test_file}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 运行测试
        result = subprocess.run(
            [sys.executable, test_file],
            cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 测试通过 (耗时: {duration:.2f}s)")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print(f"❌ 测试失败 (耗时: {duration:.2f}s)")
            print("错误输出:")
            print(result.stderr)
            if result.stdout:
                print("标准输出:")
                print(result.stdout)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ 测试超时 (>{timeout}s)")
        return False
    except Exception as e:
        print(f"💥 测试异常: {e}")
        return False

def run_core_tests():
    """运行核心功能测试"""
    print(f"\n🔧 核心功能测试")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("test/simple_test.py", "基础功能快速验证"),
        ("test/test_framework.py", "框架核心功能测试"),
        ("test/standalone_test.py", "独立测试（不依赖外部库）"),
        ("test/test_factor_analysis_complete.py", "完整因子分析流程测试"),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_file, description in tests:
        if run_test(test_file, description):
            passed += 1
    
    print(f"\n📊 核心测试结果: {passed}/{total} 通过")
    return passed, total

def run_gui_tests():
    """运行GUI测试（非阻塞）"""
    print(f"\n🖥️  GUI功能测试")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("test/verify_factor_panel.py", "因子面板验证测试"),
        ("test/test_factor_panel.py", "因子面板功能测试"),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_file, description in tests:
        if run_test(test_file, description, timeout=30):
            passed += 1
    
    print(f"\n📊 GUI测试结果: {passed}/{total} 通过")
    print("注意: GUI界面测试需要手动关闭窗口")
    return passed, total

def run_debug_tools():
    """运行调试工具"""
    print(f"\n🔍 调试工具测试")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("test/debug_factor_index.py", "因子索引结构调试"),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_file, description in tests:
        if run_test(test_file, description):
            passed += 1
    
    print(f"\n📊 调试工具结果: {passed}/{total} 通过")
    return passed, total

def main():
    """主函数"""
    print("🚀 因子研究框架测试套件")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    total_passed = 0
    total_tests = 0
    
    # 运行核心测试
    core_passed, core_total = run_core_tests()
    total_passed += core_passed
    total_tests += core_total
    
    # 运行调试工具
    debug_passed, debug_total = run_debug_tools()
    total_passed += debug_passed
    total_tests += debug_total
    
    # 询问是否运行GUI测试
    print(f"\n❓ 是否运行GUI测试？(需要图形界面环境)")
    print("GUI测试会打开窗口，需要手动关闭")
    
    try:
        choice = input("运行GUI测试? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            gui_passed, gui_total = run_gui_tests()
            total_passed += gui_passed
            total_tests += gui_total
    except KeyboardInterrupt:
        print("\n用户取消GUI测试")
    
    # 总结
    print(f"\n{'='*80}")
    print(f"🏁 测试完成")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总体结果: {total_passed}/{total_tests} 通过")
    
    if total_passed == total_tests:
        print("🎉 所有测试都通过了！")
        return 0
    else:
        print(f"⚠️  有 {total_tests - total_passed} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
