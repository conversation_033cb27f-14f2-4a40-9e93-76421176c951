import duckdb
import pandas as pd
import json
import threading
import os
from common.config import DATA_BASE_DIR

class SnapshotDB:
    """
    管理账户快照的数据库（duckdb），支持插入和查询快照
    """
    def __init__(self, db_path=None):
        # 默认快照数据库路径
        if db_path is None:
            db_path = os.path.join(DATA_BASE_DIR, "snapshots.duckdb")
        self.db_path = db_path
        self.conn = duckdb.connect(database=db_path, read_only=False)
        self._lock = threading.Lock()
        self._ensure_table()

    def _ensure_table(self):
        # 自动删除旧表，避免id字段残留
        # 问题：每次实例化都会 DROP TABLE，导致历史快照丢失！
        # 正确做法：只用 CREATE TABLE IF NOT EXISTS，不要 DROP TABLE
        # self.conn.execute("DROP TABLE IF EXISTS account_snapshots")
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS account_snapshots (
            account_id VARCHAR,
            date VARCHAR,
            value DOUBLE,
            extra_json VARCHAR
        )
        """)

    def insert_snapshot(self, account_id, date, value, extra_json=None):
        """
        插入一条快照记录
        """
        with self._lock:
            # DuckDB 不支持重复插入同一主键（如 account_id+date），可先尝试删除同日快照再插入
            self.conn.execute(
                "DELETE FROM account_snapshots WHERE account_id=? AND date=?",
                [account_id, str(date)]
            )
            self.conn.execute(
                "INSERT INTO account_snapshots (account_id, date, value, extra_json) VALUES (?, ?, ?, ?)",
                [account_id, str(date), float(value), json.dumps(extra_json) if extra_json else None]
            )

    def insert_snapshots_df(self, df: pd.DataFrame):
        """
        批量插入快照，df需包含account_id, date, value, extra_json列
        """
        with self._lock:
            self.conn.register("tmp_snapshots", df)
            self.conn.execute(
                "INSERT INTO account_snapshots SELECT * FROM tmp_snapshots"
            )
            self.conn.unregister("tmp_snapshots")

    def query_snapshots(self, account_id, start_date=None, end_date=None) -> pd.DataFrame:
        """
        查询某账户的快照，支持日期范围，返回DataFrame
        """
        sql = "SELECT date, value, extra_json FROM account_snapshots WHERE account_id=?"
        params = [account_id]
        if start_date:
            sql += " AND date >= ?"
            params.append(str(start_date))
        if end_date:
            sql += " AND date <= ?"
            params.append(str(end_date))
        sql += " ORDER BY date ASC"
        df = self.conn.execute(sql, params).df()
        # 解析extra_json为dict
        if not df.empty and "extra_json" in df.columns:
            df["extra_json"] = df["extra_json"].apply(lambda x: json.loads(x) if isinstance(x, str) and x else None)
        return df

    def delete_snapshots(self, account_id):
        """
        删除某账户的所有快照
        """
        with self._lock:
            self.conn.execute("DELETE FROM account_snapshots WHERE account_id=?", [account_id])

    def clear_test_snapshots_except_init(self, account_id):
        """
        删除test账户除初始镜像(date='init')外的所有快照
        """
        with self._lock:
            self.conn.execute(
                "DELETE FROM account_snapshots WHERE account_id=? AND date != ?",
                [account_id, "init"]
            )

    def close(self):
        self.conn.close()
