"""
数据适配器

与现有数据库系统集成，提供统一的数据接口。
支持股票价格、财务数据、行业分类等多种数据类型的获取和格式转换。
"""

from typing import List, Dict, Tuple, Optional, Union
import pandas as pd
import numpy as np
import logging
from datetime import datetime, date

logger = logging.getLogger(__name__)


class DataAdapter:
    """
    数据适配器，负责与现有数据库系统集成
    
    主要功能:
    - 从现有数据库获取价格数据
    - 获取基本面数据
    - 获取行业分类数据
    - 数据格式转换和清洗
    - 为alphalens提供标准格式数据
    """
    
    def __init__(self, db_manage, cache_enabled: bool = True, cache_size: int = 1000):
        """
        初始化数据适配器
        
        Parameters:
        -----------
        db_manage : DBManage
            数据库管理对象
        cache_enabled : bool, optional
            是否启用缓存，默认True
        cache_size : int, optional
            缓存大小，默认1000
        """
        self.db_manage = db_manage
        self.cache_enabled = cache_enabled
        self.cache_size = cache_size
        self._price_cache = {}
        self._fundamental_cache = {}
        self._industry_cache = {}
        
        # 数据字段映射
        self._price_field_mapping = {
            'date': 'date',
            'open': 'open', 
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'volume': 'volume',
            'amount': 'amount',
            'turnover_rate': 'turnover_rate',
            'pe_dynamic': 'pe',
            'pb': 'pb'
        }
        
    def get_price_data(self, assets: List[str], start_date: str, end_date: str,
                      freq: str = "daily", fields: List[str] = None) -> pd.DataFrame:
        """
        获取价格数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期，格式 'YYYY-MM-DD'
        end_date : str
            结束日期，格式 'YYYY-MM-DD'
        freq : str, optional
            数据频率，'daily', 'weekly', 'monthly'，默认'daily'
        fields : List[str], optional
            需要的字段列表，默认获取所有价格字段
            
        Returns:
        --------
        pd.DataFrame
            MultiIndex (date, asset) 的价格数据
            columns: ['open', 'high', 'low', 'close', 'volume', 'amount', ...]
        """
        if fields is None:
            fields = ['open', 'high', 'low', 'close', 'volume', 'amount']
            
        # 检查缓存
        cache_key = f"price_{'-'.join(assets)}_{start_date}_{end_date}_{freq}"
        if self.cache_enabled and cache_key in self._price_cache:
            logger.debug(f"Using cached price data for {cache_key}")
            cached_data = self._price_cache[cache_key]
            return cached_data[fields] if fields else cached_data
            
        try:
            price_data_list = []

            for asset in assets:
                # 从数据库获取数据
                df = self._get_asset_price_data(asset, start_date, end_date, freq)

                if not df.empty:
                    # 添加资产标识
                    df['asset'] = asset
                    price_data_list.append(df)
                else:
                    logger.warning(f"No price data found for asset {asset}")

            if not price_data_list:
                logger.warning("No price data found for any assets, generating mock data for testing")
                # 生成模拟数据用于测试
                mock_df = self._generate_mock_price_data(assets, start_date, end_date, fields)
                return mock_df
                
            # 合并所有资产数据
            combined_df = pd.concat(price_data_list, ignore_index=True)
            
            # 数据清洗和格式转换
            processed_df = self._process_price_data(combined_df)
            
            # 缓存数据
            if self.cache_enabled:
                self._price_cache[cache_key] = processed_df
                self._manage_cache_size()
                
            logger.info(f"Successfully loaded price data for {len(assets)} assets, "
                       f"{len(processed_df)} records")
            
            return processed_df[fields] if fields else processed_df
            
        except Exception as e:
            logger.error(f"Error loading price data: {str(e)}")
            raise
            
    def get_fundamental_data(self, assets: List[str], start_date: str, end_date: str,
                           fields: List[str] = None) -> pd.DataFrame:
        """
        获取基本面数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段列表，如 ['pe', 'pb', 'roe', 'revenue']
            
        Returns:
        --------
        pd.DataFrame
            MultiIndex (date, asset) 的基本面数据
        """
        if fields is None:
            fields = ['pe', 'pb', 'roe', 'roa', 'revenue', 'net_profit']
            
        # 检查缓存
        cache_key = f"fundamental_{'-'.join(assets)}_{start_date}_{end_date}"
        if self.cache_enabled and cache_key in self._fundamental_cache:
            logger.debug(f"Using cached fundamental data for {cache_key}")
            cached_data = self._fundamental_cache[cache_key]
            return cached_data[fields] if fields else cached_data
            
        try:
            # 注意：这里需要根据实际的基本面数据表结构来实现
            # 目前先返回空DataFrame，后续根据实际数据结构完善
            logger.warning("Fundamental data not implemented yet")
            
            # 创建空的DataFrame作为占位符
            index = pd.MultiIndex.from_tuples([], names=['date', 'asset'])
            fundamental_df = pd.DataFrame(index=index, columns=fields)
            
            return fundamental_df
            
        except Exception as e:
            logger.error(f"Error loading fundamental data: {str(e)}")
            raise
            
    def get_industry_classification(self, assets: List[str]) -> pd.Series:
        """
        获取行业分类数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
            
        Returns:
        --------
        pd.Series
            Index为股票代码，值为行业分类的序列
        """
        # 检查缓存
        cache_key = f"industry_{'-'.join(sorted(assets))}"
        if self.cache_enabled and cache_key in self._industry_cache:
            logger.debug(f"Using cached industry data for {cache_key}")
            return self._industry_cache[cache_key]
            
        try:
            # 注意：这里需要根据实际的行业分类数据来实现
            # 目前先返回模拟数据，后续根据实际数据结构完善
            logger.warning("Industry classification not implemented yet")
            
            # 创建模拟的行业分类数据
            industry_mapping = {
                '000001': '银行',
                '000002': '房地产',
                '600000': '银行',
                '600036': '银行',
                '000858': '白酒'
            }
            
            industry_series = pd.Series(
                [industry_mapping.get(asset, '其他') for asset in assets],
                index=assets,
                name='industry'
            )
            
            # 缓存数据
            if self.cache_enabled:
                self._industry_cache[cache_key] = industry_series
                
            return industry_series
            
        except Exception as e:
            logger.error(f"Error loading industry classification: {str(e)}")
            raise
            
    def format_for_alphalens(self, factor_data: pd.DataFrame, 
                           price_data: pd.DataFrame = None,
                           groupby_data: pd.Series = None) -> Tuple[pd.Series, pd.DataFrame, pd.Series]:
        """
        将数据格式化为alphalens所需格式
        
        Parameters:
        -----------
        factor_data : pd.DataFrame
            因子数据
        price_data : pd.DataFrame, optional
            价格数据，如果为None则自动获取
        groupby_data : pd.Series, optional
            分组数据，如行业分类
            
        Returns:
        --------
        Tuple[pd.Series, pd.DataFrame, pd.Series]
            (factor_data, pricing_data, groupby_data) 元组
        """
        try:
            # 处理因子数据
            if isinstance(factor_data, pd.DataFrame):
                if factor_data.shape[1] == 1:
                    factor_series = factor_data.iloc[:, 0]
                else:
                    raise ValueError("Factor data should have only one column")
            elif isinstance(factor_data, pd.Series):
                factor_series = factor_data
            else:
                raise ValueError("Factor data must be DataFrame or Series")
                
            # 确保因子数据有正确的MultiIndex
            if not isinstance(factor_series.index, pd.MultiIndex):
                raise ValueError("Factor data must have MultiIndex (date, asset)")
                
            # 处理价格数据
            if price_data is None:
                # 自动获取价格数据
                assets = factor_series.index.get_level_values(1).unique().tolist()
                start_date = factor_series.index.get_level_values(0).min().strftime('%Y-%m-%d')
                end_date = factor_series.index.get_level_values(0).max().strftime('%Y-%m-%d')
                
                price_data = self.get_price_data(assets, start_date, end_date)
                
            # 提取收盘价
            if 'close' not in price_data.columns:
                raise ValueError("Price data must contain 'close' column")
                
            pricing_series = price_data['close']
            
            # 处理分组数据
            if groupby_data is None:
                assets = factor_series.index.get_level_values(1).unique().tolist()
                groupby_data = self.get_industry_classification(assets)
                
            logger.info("Successfully formatted data for alphalens")
            
            return factor_series, pricing_series, groupby_data
            
        except Exception as e:
            logger.error(f"Error formatting data for alphalens: {str(e)}")
            raise
            
    def _get_asset_price_data(self, asset: str, start_date: str, end_date: str, freq: str) -> pd.DataFrame:
        """
        获取单个资产的价格数据
        
        Parameters:
        -----------
        asset : str
            资产代码
        start_date : str
            开始日期
        end_date : str
            结束日期
        freq : str
            数据频率
            
        Returns:
        --------
        pd.DataFrame
            价格数据
        """
        try:
            # 使用现有的HistDB查询数据
            df = self.db_manage.hist_db.query_hist_data(
                category=None,  # 自动判断股票还是ETF
                freq=freq,
                code=asset,
                start_date=start_date,
                end_date=end_date
            )
            
            return df
            
        except Exception as e:
            logger.error(f"Error getting price data for {asset}: {str(e)}")
            return pd.DataFrame()
            
    def _process_price_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理价格数据
        
        Parameters:
        -----------
        df : pd.DataFrame
            原始价格数据
            
        Returns:
        --------
        pd.DataFrame
            处理后的价格数据，MultiIndex (date, asset)
        """
        try:
            # 确保日期列存在且格式正确
            if 'date' not in df.columns:
                raise ValueError("Price data must contain 'date' column")
                
            # 转换日期格式
            df['date'] = pd.to_datetime(df['date'])
            
            # 设置MultiIndex
            df = df.set_index(['date', 'asset'])
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    
            # 删除重复数据
            df = df[~df.index.duplicated(keep='first')]
            
            # 按日期排序
            df = df.sort_index()
            
            return df
            
        except Exception as e:
            logger.error(f"Error processing price data: {str(e)}")
            raise
            
    def _manage_cache_size(self):
        """管理缓存大小"""
        if len(self._price_cache) > self.cache_size:
            # 删除最旧的缓存项
            oldest_key = next(iter(self._price_cache))
            del self._price_cache[oldest_key]

    def _generate_mock_price_data(self, assets: List[str], start_date: str, end_date: str,
                                 fields: List[str] = None) -> pd.DataFrame:
        """
        生成模拟价格数据用于测试

        Parameters:
        -----------
        assets : List[str]
            资产代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段列表

        Returns:
        --------
        pd.DataFrame
            模拟的价格数据，MultiIndex (date, asset)
        """
        import numpy as np

        if fields is None:
            fields = ['open', 'high', 'low', 'close', 'volume', 'amount']

        # 生成日期范围（只包含工作日）
        date_range = pd.bdate_range(start=start_date, end=end_date, freq='B')

        # 创建MultiIndex
        index = pd.MultiIndex.from_product([date_range, assets], names=['date', 'asset'])

        # 生成模拟数据
        np.random.seed(42)  # 固定随机种子以便重现
        n_records = len(index)

        data = {}

        # 生成基础价格（随机游走）
        base_price = 100.0
        for i, asset in enumerate(assets):
            asset_records = len(date_range)

            # 为每个资产生成价格序列
            returns = np.random.normal(0.001, 0.02, asset_records)  # 日收益率
            prices = [base_price + i * 10]  # 不同资产的起始价格

            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))

            # 为每个日期生成OHLC数据
            for j, price in enumerate(prices):
                idx = i * asset_records + j

                # 生成日内波动
                daily_vol = abs(np.random.normal(0, 0.01))
                high = price * (1 + daily_vol)
                low = price * (1 - daily_vol)
                open_price = price * (1 + np.random.normal(0, 0.005))

                if 'open' in fields:
                    data.setdefault('open', []).append(open_price)
                if 'high' in fields:
                    data.setdefault('high', []).append(high)
                if 'low' in fields:
                    data.setdefault('low', []).append(low)
                if 'close' in fields:
                    data.setdefault('close', []).append(price)
                if 'volume' in fields:
                    data.setdefault('volume', []).append(np.random.randint(1000000, 10000000))
                if 'amount' in fields:
                    data.setdefault('amount', []).append(price * data.get('volume', [1000000])[-1])

        # 创建DataFrame
        mock_df = pd.DataFrame(data, index=index)

        logger.info(f"Generated mock price data: {mock_df.shape[0]} records for {len(assets)} assets")

        return mock_df
            
    def clear_cache(self):
        """清空缓存"""
        self._price_cache.clear()
        self._fundamental_cache.clear()
        self._industry_cache.clear()
        logger.info("Data adapter cache cleared")
        
    def get_cache_info(self) -> Dict[str, int]:
        """
        获取缓存信息
        
        Returns:
        --------
        Dict[str, int]
            缓存信息
        """
        return {
            'price_cache_size': len(self._price_cache),
            'fundamental_cache_size': len(self._fundamental_cache),
            'industry_cache_size': len(self._industry_cache),
            'total_cache_size': len(self._price_cache) + len(self._fundamental_cache) + len(self._industry_cache)
        }
