from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QLineEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QDateEdit, QMessageBox, QHeaderView, QDialog, QDialogButtonBox, QTextEdit, QGroupBox
)
from PyQt5.QtCore import QDate
import os
import json
from common.config import PERIOD_MAP, ADJ_MAP

class PanelConfigTrade(QWidget):
    def __init__(self, parent=None, account_manage=None):
        super().__init__(parent)
        self.account_manage = account_manage
        self._init_ui()

    def _init_ui(self):
        layout = QVBoxLayout(self)

        # 模拟参数配置
        sim_box = QHBoxLayout()
        sim_box.addWidget(QLabel("基准选取"))
        self.combo_benchmark = QComboBox()
        self.combo_benchmark.addItems(["沪深300", "上证指数", "中证500"])
        sim_box.addWidget(self.combo_benchmark)
        sim_box.addWidget(QLabel("初始资金"))
        self.text_init_cash = QLineEdit("100000")
        sim_box.addWidget(self.text_init_cash)
        sim_box.addWidget(QLabel("滑点(%)"))
        self.text_slippage_sim = QLineEdit("0.1")
        sim_box.addWidget(self.text_slippage_sim)
        sim_box.addWidget(QLabel("手续费(%)"))
        self.text_fee_sim = QLineEdit("0.05")
        sim_box.addWidget(self.text_fee_sim)
        layout.addLayout(sim_box)

        # 交易参数配置
        trade_box = QHBoxLayout()
        trade_box.addWidget(QLabel("开始日期"))
        self.date_start = QDateEdit()
        self.date_start.setDate(QDate.currentDate())
        trade_box.addWidget(self.date_start)
        trade_box.addWidget(QLabel("结束日期"))
        self.date_end = QDateEdit()
        self.date_end.setDate(QDate.currentDate())
        trade_box.addWidget(self.date_end)
        trade_box.addWidget(QLabel("交易周期"))
        self.combo_period = QComboBox()
        self.combo_period.addItems(["日线", "周线", "月线"])
        trade_box.addWidget(self.combo_period)
        trade_box.addWidget(QLabel("股票复权"))
        self.combo_adj = QComboBox()
        self.combo_adj.addItems(["不复权", "前复权", "后复权"])
        trade_box.addWidget(self.combo_adj)
        layout.addLayout(trade_box)

        # --- 策略配置 ---
        strategies_layout = QHBoxLayout()

        # 选股策略
        equity_sel_group = QGroupBox("选股策略")
        equity_sel_vbox = QVBoxLayout()
        self.combo_equity_sel_strategy, self.equity_sel_strategy_list = self._create_strategy_combo(
            "strategies/sel_strategy",
            self._on_select_equity_sel_strategy,
            "selected_equity_strategy"
        )
        equity_sel_vbox.addWidget(self.combo_equity_sel_strategy)
        equity_sel_vbox.addWidget(QLabel("参数:"))
        self.equity_sel_param_text = QTextEdit()
        self.equity_sel_param_text.setReadOnly(False)
        equity_sel_vbox.addWidget(self.equity_sel_param_text)
        equity_sel_group.setLayout(equity_sel_vbox)
        strategies_layout.addWidget(equity_sel_group)

        # 择时策略
        timing_group = QGroupBox("择时策略")
        timing_vbox = QVBoxLayout()
        self.combo_timing_strategy, self.timing_strategy_list = self._create_strategy_combo(
            "strategies/trade_strategy",
            self._on_select_timing_strategy,
            "timing_strategy"
        )
        timing_vbox.addWidget(self.combo_timing_strategy)
        timing_vbox.addWidget(QLabel("参数:"))
        self.timing_param_text = QTextEdit()
        self.timing_param_text.setReadOnly(False)
        timing_vbox.addWidget(self.timing_param_text)
        timing_group.setLayout(timing_vbox)
        strategies_layout.addWidget(timing_group)

        # 仓位管理
        sizer_group = QGroupBox("仓位管理")
        sizer_vbox = QVBoxLayout()
        self.combo_sizer_strategy, self.sizer_strategy_list = self._create_strategy_combo(
            "portfolio/sizer",
            self._on_select_sizer_strategy,
            "selected_sizer_strategy"
        )
        sizer_vbox.addWidget(self.combo_sizer_strategy)
        sizer_vbox.addWidget(QLabel("参数:"))
        self.sizer_param_text = QTextEdit()
        self.sizer_param_text.setReadOnly(False)
        sizer_vbox.addWidget(self.sizer_param_text)
        sizer_group.setLayout(sizer_vbox)
        strategies_layout.addWidget(sizer_group)

        layout.addLayout(strategies_layout, 1)

        # 股票池表格及+按钮
        stock_table_box = QVBoxLayout()
        stock_table_hbox = QHBoxLayout()
        stock_table_hbox.addStretch(1)
        self.btn_add_stock = QPushButton("+")
        self.btn_add_stock.setFixedWidth(28)
        self.btn_add_stock.setToolTip("手动添加股票")
        self.btn_add_stock.clicked.connect(self._on_add_stock)
        stock_table_hbox.addWidget(self.btn_add_stock)
        self.btn_del_stock = QPushButton("-")
        self.btn_del_stock.setFixedWidth(28)
        self.btn_del_stock.setToolTip("删除选中股票")
        self.btn_del_stock.clicked.connect(self._on_del_stock)
        stock_table_hbox.addWidget(self.btn_del_stock)
        stock_table_box.addLayout(stock_table_hbox)
        self.stock_table = QTableWidget(0, 8)
        self.stock_table.setHorizontalHeaderLabels(["代码", "名称", "交易策略", "策略参数", "仓位管理", "仓位参数", "当前价", "配置比例"])
        self.stock_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        stock_table_box.addWidget(self.stock_table)
        layout.addLayout(stock_table_box, 2)

        # 操作按钮
        btn_box = QHBoxLayout()
        self.btn_run_sel = QPushButton("运行选股策略")
        btn_box.addWidget(self.btn_run_sel)
        self.btn_config_strategy = QPushButton("配置策略")
        btn_box.addWidget(self.btn_config_strategy)
        self.btn_load = QPushButton("加载行情数据")
        btn_box.addWidget(self.btn_load)
        btn_box.addStretch()
        self.btn_ok = QPushButton("确定")
        self.btn_close = QPushButton("返回")
        btn_box.addWidget(self.btn_ok)
        btn_box.addWidget(self.btn_close)
        layout.addLayout(btn_box)

        # 绑定按钮事件
        self.btn_ok.clicked.connect(self._on_ok)
        self.btn_close.clicked.connect(self._on_close)
        self.btn_config_strategy.clicked.connect(self._on_config_strategy_to_stock)
        self.btn_run_sel.clicked.connect(self._on_run_equity_sel_strategy)
        self.btn_load.clicked.connect(self._on_load_market_data)

        # 初始化时显示所有策略的参数
        self._on_select_equity_sel_strategy(self.combo_equity_sel_strategy.currentText())
        self._on_select_timing_strategy(self.combo_timing_strategy.currentText())
        self._on_select_sizer_strategy(self.combo_sizer_strategy.currentText())

    def _on_add_stock(self):
        row = self.stock_table.rowCount()
        self.stock_table.insertRow(row)
        for col in range(self.stock_table.columnCount()):
            self.stock_table.setItem(row, col, QTableWidgetItem(""))

    def _on_del_stock(self):
        selected = self.stock_table.selectedItems()
        if not selected:
            QMessageBox.information(self, "提示", "请先选择要删除的标的")
            return
        selected_rows = sorted(set(item.row() for item in selected), reverse=True)
        for row in selected_rows:
            self.stock_table.removeRow(row)

    def _on_ok(self):
        # 保存配置信息到账户
        account_manage = self.account_manage  # 直接用构造参数传递的account_manage
        account_name = ""
        parent_dialog = self.parent()
        if isinstance(parent_dialog, QDialog):
            title = parent_dialog.windowTitle()
            if " - " in title:
                account_name = title.split(" - ", 1)[-1]
        if not account_name or account_manage is None:
            QMessageBox.warning(self, "错误", "无法获取账户名或账户管理器，无法保存配置")
            return

        # 收集配置信息
        equity_pool = []
        for i in range(self.stock_table.rowCount()):
            code = self.stock_table.item(i, 0).text() if self.stock_table.item(i, 0) else ""
            name = self.stock_table.item(i, 1).text() if self.stock_table.item(i, 1) else ""
            strategy_name = self.stock_table.item(i, 2).text() if self.stock_table.item(i, 2) else ""
            strategy_params_str = self.stock_table.item(i, 3).text() if self.stock_table.item(i, 3) else "{}"
            sizer_strategy_name = self.stock_table.item(i, 4).text() if self.stock_table.item(i, 4) else ""
            sizer_params_str = self.stock_table.item(i, 5).text() if self.stock_table.item(i, 5) else "{}"

            try:
                strategy_params = json.loads(strategy_params_str) if strategy_params_str.strip() else {}
                sizer_params = json.loads(sizer_params_str) if sizer_params_str.strip() else {}
            except json.JSONDecodeError as e:
                QMessageBox.warning(self, "错误", f"行 {i+1} 的策略或仓位参数不是有效的JSON格式: {e}")
                return

            strategy = {"name": strategy_name, "params": strategy_params} if strategy_name else {}
            sizer = {"name": sizer_strategy_name, "params": sizer_params} if sizer_strategy_name else {}
            if code:
                equity_pool.append({
                    "code": code,
                    "name": name,
                    "strategy": strategy,
                    "sizer": sizer,
                })
        
        # 获取选股策略及其参数
        sel_strategy_name = self.combo_equity_sel_strategy.currentText()
        sel_strategy_params_str = self.equity_sel_param_text.toPlainText()
        sel_strategy_params = {}
        try:
            if sel_strategy_params_str.strip():
                sel_strategy_params = json.loads(sel_strategy_params_str)
        except json.JSONDecodeError:
            QMessageBox.warning(self, "错误", "选股策略参数格式不正确，必须是有效的JSON。")
            return
        
        equity_sel_strategy_config = {
            "name": sel_strategy_name,
            "params": sel_strategy_params
        }

        init_cash = float(self.text_init_cash.text())
        config = {
            "benchmark": self.combo_benchmark.currentText(),
            "init_cash": init_cash,
            "cash": init_cash,
            "total_assets": init_cash,
            "slippage": self.text_slippage_sim.text(),
            "fee": self.text_fee_sim.text(),
            "start_time": self.date_start.date().toString("yyyy-MM-dd"),
            "end_time": self.date_end.date().toString("yyyy-MM-dd"),
            "period": self.combo_period.currentText(),
            "adj": self.combo_adj.currentText(),
            "equity_pool": equity_pool,
            "equity_sel_strategy": equity_sel_strategy_config,  # 保存选股策略和参数
        }
        config = self._config_format_convert(config)
        try:
            account_manage._update_account(account_name, config)
            QMessageBox.information(self, "提示", "配置已保存")
            if isinstance(parent_dialog, QDialog):
                parent_dialog.accept()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存配置失败: {e}")

    def _on_close(self):
        parent = self.parent()
        if isinstance(parent, QDialog):
            parent.reject()

    def _config_format_convert(self, config):
        config = config.copy()
        if "period" in config:
            config["period"] = PERIOD_MAP.get(config["period"], config["period"])
        if "adj" in config:
            config["adj"] = ADJ_MAP.get(config["adj"], config["adj"])
        return config

    def _get_strategy_list(self, strategy_dir):
        """
        自动扫描策略目录，返回策略名列表
        """
        base_path = os.path.join(os.path.dirname(__file__), "../../", strategy_dir)
        base_path = os.path.abspath(base_path)
        if not os.path.isdir(base_path):
            return []
        strategy_list = []
        for name in os.listdir(base_path):
            if os.path.isdir(os.path.join(base_path, name)) and not name.startswith("__"):
                strategy_list.append(name)
        return strategy_list

    def _create_strategy_combo(self, strategy_dir, on_change, attr_name):
        """
        创建策略下拉框及其逻辑，返回(combo, 策略列表)
        """
        strategy_list = self._get_strategy_list(strategy_dir)
        combo = QComboBox()
        combo.addItems(strategy_list)
        if strategy_list:
            combo.setCurrentIndex(0)
            setattr(self, attr_name, strategy_list[0])
        combo.currentTextChanged.connect(on_change)
        return combo, strategy_list

    def _on_select_timing_strategy(self, value):
        self.timing_strategy = value
        self._show_strategy_params("strategies/trade_strategy", value, self.timing_param_text)

    def _on_select_equity_sel_strategy(self, value):
        self.selected_equity_strategy = value
        self._show_strategy_params("strategies/sel_strategy", value, self.equity_sel_param_text)

    def _on_select_sizer_strategy(self, value):
        self.selected_sizer_strategy = value
        self._show_strategy_params("portfolio/sizer", value, self.sizer_param_text)

    def _show_strategy_params(self, strategy_dir, strategy_name, text_widget):
        """
        根据策略名加载并显示参数
        """
        if not strategy_name:
            text_widget.setText("{}")
            return

        param_path = os.path.join(
            os.path.dirname(__file__),
            "../../",
            strategy_dir,
            strategy_name,
            "params.json"
        )
        param_path = os.path.abspath(param_path)
        param_str = ""
        if os.path.isfile(param_path):
            try:
                with open(param_path, "r", encoding="utf-8") as f:
                    params = json.load(f)
                # 支持多版本，取第一个或最后一个
                if isinstance(params, list) and params:
                    param_obj = params[-1].get("parameters", params[-1].get("params", {}))
                elif isinstance(params, dict):
                    param_obj = params
                else:
                    param_obj = {}
                param_str = json.dumps(param_obj, ensure_ascii=False, indent=2)
            except Exception as e:
                param_str = f'{{"error": "参数加载失败: {e}"}}'
        else:
            param_str = "{}"  # 无参数时显示空JSON
        text_widget.setText(param_str)

    def _on_config_strategy_to_stock(self):
        # 将当前下拉框选中的交易策略、择时策略、仓位管理，设置到股票池选中行
        trade_strategy = self.combo_timing_strategy.currentText() if hasattr(self, "combo_timing_strategy") else ""
        sizer_strategy = self.combo_sizer_strategy.currentText() if hasattr(self, "combo_sizer_strategy") else ""
        trade_params_str = self.timing_param_text.toPlainText()
        sizer_params_str = self.sizer_param_text.toPlainText()

        try:
            json.loads(trade_params_str)
            json.loads(sizer_params_str)
        except json.JSONDecodeError as e:
            QMessageBox.warning(self, "错误", f"参数格式不正确，必须是有效的JSON: {e}")
            return

        if not trade_strategy:
            QMessageBox.information(self, "提示", "请先在择时策略下拉框中选择一个策略")
            return
        if not sizer_strategy:
            QMessageBox.information(self, "提示", "请先在仓位管理下拉框中选择一个策略")
            return
        selected = self.stock_table.selectedItems()
        if not selected:
            QMessageBox.information(self, "提示", "请先在股票池中选择要配置的标的")
            return
        selected_rows = set(item.row() for item in selected)
        for row in selected_rows:
            self.stock_table.setItem(row, 2, QTableWidgetItem(trade_strategy))
            self.stock_table.setItem(row, 3, QTableWidgetItem(trade_params_str))
            self.stock_table.setItem(row, 4, QTableWidgetItem(sizer_strategy))
            self.stock_table.setItem(row, 5, QTableWidgetItem(sizer_params_str))
        QMessageBox.information(self, "提示", "已将策略及参数分配到选中标的")

    def _on_run_equity_sel_strategy(self):
        # 运行选股策略的select方法，并将结果显示到股票池列表
        from importlib import import_module
        strategy_name = getattr(self, "selected_equity_strategy", None)
        if not strategy_name:
            QMessageBox.information(self, "提示", "请先选择选股策略")
            return
        
        # 获取UI上的参数
        params_str = self.equity_sel_param_text.toPlainText()
        params = {}
        try:
            if params_str.strip():
                params = json.loads(params_str)
        except json.JSONDecodeError:
            QMessageBox.warning(self, "错误", "选股策略参数格式不正确，必须是有效的JSON。")
            return

        try:
            module_path = f"strategies.sel_strategy.{strategy_name}.selection_strategy"
            module = import_module(module_path)
            strategy_cls = getattr(module, "SelectionStrategy")
            # 实例化时传入参数
            strategy_instance = strategy_cls(**params)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"选股策略加载失败: {e}")
            return
        try:
            result = strategy_instance.select(None)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"选股策略运行失败: {e}")
            return
        self.stock_table.setRowCount(0)
        for item in result:
            code = item.get("code", "")
            name = item.get("name", "")
            strategy = item.get("strategy", "")
            params = item.get("params", {})
            allocation_ratio = item.get("allocation_ratio", item.get("alloction_ratio", ""))
            row = self.stock_table.rowCount()
            self.stock_table.insertRow(row)
            self.stock_table.setItem(row, 0, QTableWidgetItem(str(code)))
            self.stock_table.setItem(row, 1, QTableWidgetItem(str(name)))
            self.stock_table.setItem(row, 2, QTableWidgetItem(str(strategy) if strategy else ""))
            self.stock_table.setItem(row, 3, QTableWidgetItem(json.dumps(params, ensure_ascii=False) if params else "{}"))
            self.stock_table.setItem(row, 4, QTableWidgetItem(""))
            self.stock_table.setItem(row, 5, QTableWidgetItem("{}"))
            self.stock_table.setItem(row, 6, QTableWidgetItem(""))
            self.stock_table.setItem(row, 7, QTableWidgetItem(str(allocation_ratio) if allocation_ratio != "" else ""))

    def _on_load_market_data(self):
        from database.realtime_db import RealTimeDB
        db = RealTimeDB()
        for row in range(self.stock_table.rowCount()):
            code = self.stock_table.item(row, 0).text() if self.stock_table.item(row, 0) else ""
            if not code:
                continue
            df = db.query_realtime(code=code)
            print(f"查询实时数据: {code}, 返回行数: {len(df) if df is not None else 0}")
            if df is not None and not df.empty:
                last_row = df.sort_values(by="date").iloc[-1]
                name = last_row.get('name', '')
                price = last_row.get('latest_price', '')
            else:
                name = ''
                price = ''
            if not name or not price:
                QMessageBox.information(self, "提示", f"实时数据库无数据: {code}")
                continue
            self.stock_table.setItem(row, 1, QTableWidgetItem(str(name)))
            self.stock_table.setItem(row, 6, QTableWidgetItem(str(price)))
