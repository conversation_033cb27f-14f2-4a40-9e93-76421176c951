"""
因子研究主入口类

提供统一的因子研究接口，整合数据获取、因子计算、分析评估和可视化功能。
"""

from typing import Dict, List, Optional, Any, Union, Tuple
import pandas as pd
import numpy as np
import logging
import yaml
from pathlib import Path

from .data_adapter import DataAdapter
from .factor_engine import FactorEngine
from .analyzer import FactorAnalyzer
from .visualizer import FactorVisualizer
from ..factors.base import BaseFactor
from ..utils.config import FactorConfig

logger = logging.getLogger(__name__)


class FactorResearch:
    """
    因子研究主入口类
    
    整合所有因子研究功能，提供简洁的API接口。
    
    主要功能:
    - 因子计算和管理
    - 因子有效性分析
    - 结果可视化
    - 报告生成
    - 批量分析
    """
    
    def __init__(self, db_manage=None, config_path: str = None, **config_kwargs):
        """
        初始化因子研究框架
        
        Parameters:
        -----------
        db_manage : DBManage, optional
            数据库管理对象
        config_path : str, optional
            配置文件路径
        **config_kwargs : dict
            配置参数
        """
        # 加载配置
        self.config = self._load_config(config_path, **config_kwargs)
        
        # 初始化各个组件
        self.data_adapter = DataAdapter(
            db_manage=db_manage,
            cache_enabled=self.config.get('data_source', {}).get('cache_enabled', True),
            cache_size=self.config.get('data_source', {}).get('cache_size', 1000)
        )
        
        self.factor_engine = FactorEngine(
            data_adapter=self.data_adapter,
            parallel_enabled=self.config.get('factor_calculation', {}).get('parallel_enabled', False),
            max_workers=self.config.get('factor_calculation', {}).get('max_workers', 4)
        )
        
        self.analyzer = FactorAnalyzer(data_adapter=self.data_adapter)
        
        self.visualizer = FactorVisualizer(
            figure_size=self.config.get('visualization', {}).get('figure_size', [12, 8]),
            dpi=self.config.get('visualization', {}).get('dpi', 300),
            style=self.config.get('visualization', {}).get('style', 'seaborn')
        )
        
        logger.info("FactorResearch initialized successfully")
        
    def calculate_factor(self, factor_name: str, assets: List[str],
                        start_date: str, end_date: str, **params) -> pd.Series:
        """
        计算指定因子
        
        Parameters:
        -----------
        factor_name : str
            因子名称
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期，格式 'YYYY-MM-DD'
        end_date : str
            结束日期，格式 'YYYY-MM-DD'
        **params : dict
            因子计算参数
            
        Returns:
        --------
        pd.Series
            MultiIndex (date, asset) 的因子值
        """
        return self.factor_engine.calculate_factor(
            factor_name=factor_name,
            assets=assets,
            start_date=start_date,
            end_date=end_date,
            **params
        )
        
    def analyze_factor(self, factor_data: pd.Series, periods: List[int] = None,
                      quantiles: int = None, max_loss: float = None,
                      groupby_data: pd.Series = None) -> Dict[str, Any]:
        """
        分析因子有效性
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据，MultiIndex (date, asset)
        periods : List[int], optional
            持有期列表，默认从配置文件读取
        quantiles : int, optional
            分位数数量，默认从配置文件读取
        max_loss : float, optional
            最大数据丢失比例，默认从配置文件读取
        groupby_data : pd.Series, optional
            分组数据，如行业分类
            
        Returns:
        --------
        Dict[str, Any]
            分析结果字典
        """
        # 使用配置文件中的默认值
        alphalens_config = self.config.get('alphalens_settings', {})
        
        if periods is None:
            periods = alphalens_config.get('periods', [1, 5, 10, 20])
        if quantiles is None:
            quantiles = alphalens_config.get('quantiles', 5)
        if max_loss is None:
            max_loss = alphalens_config.get('max_loss', 0.35)
            
        return self.analyzer.analyze_factor(
            factor_data=factor_data,
            periods=periods,
            quantiles=quantiles,
            max_loss=max_loss,
            groupby_data=groupby_data
        )
        
    def calculate_and_analyze_factor(self, factor_name: str, assets: List[str],
                                   start_date: str, end_date: str,
                                   periods: List[int] = None, quantiles: int = None,
                                   **params) -> Tuple[pd.Series, Dict[str, Any]]:
        """
        计算并分析因子（一步完成）
        
        Parameters:
        -----------
        factor_name : str
            因子名称
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        periods : List[int], optional
            持有期列表
        quantiles : int, optional
            分位数数量
        **params : dict
            因子计算参数
            
        Returns:
        --------
        Tuple[pd.Series, Dict[str, Any]]
            (因子数据, 分析结果)
        """
        # 计算因子
        factor_data = self.calculate_factor(
            factor_name=factor_name,
            assets=assets,
            start_date=start_date,
            end_date=end_date,
            **params
        )
        
        # 分析因子
        analysis_results = self.analyze_factor(
            factor_data=factor_data,
            periods=periods,
            quantiles=quantiles
        )
        
        return factor_data, analysis_results
        
    def batch_analyze_factors(self, factor_names: List[str], assets: List[str],
                            start_date: str, end_date: str,
                            periods: List[int] = None, quantiles: int = None,
                            **params) -> Dict[str, Dict[str, Any]]:
        """
        批量分析多个因子
        
        Parameters:
        -----------
        factor_names : List[str]
            因子名称列表
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        periods : List[int], optional
            持有期列表
        quantiles : int, optional
            分位数数量
        **params : dict
            因子计算参数
            
        Returns:
        --------
        Dict[str, Dict[str, Any]]
            {factor_name: analysis_results} 的字典
        """
        results = {}
        
        # 批量计算因子
        factor_data_dict = self.factor_engine.calculate_multiple_factors(
            factor_names=factor_names,
            assets=assets,
            start_date=start_date,
            end_date=end_date,
            **params
        )
        
        # 分别分析每个因子
        for factor_name, factor_data in factor_data_dict.items():
            try:
                analysis_results = self.analyze_factor(
                    factor_data=factor_data,
                    periods=periods,
                    quantiles=quantiles
                )
                results[factor_name] = analysis_results
                logger.info(f"Completed analysis for factor: {factor_name}")
                
            except Exception as e:
                logger.error(f"Error analyzing factor {factor_name}: {str(e)}")
                continue
                
        return results
        
    def compare_factors(self, factor_data_dict: Dict[str, pd.Series],
                       periods: List[int] = None) -> Dict[str, Any]:
        """
        比较多个因子的表现
        
        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            {factor_name: factor_data} 的字典
        periods : List[int], optional
            持有期列表
            
        Returns:
        --------
        Dict[str, Any]
            因子比较结果
        """
        return self.analyzer.compare_factors(factor_data_dict, periods)
        
    def plot_factor_analysis(self, analysis_results: Dict[str, Any],
                           save_path: str = None, factor_name: str = "Factor") -> List:
        """
        绘制因子分析图表
        
        Parameters:
        -----------
        analysis_results : Dict[str, Any]
            分析结果
        save_path : str, optional
            保存路径
        factor_name : str, optional
            因子名称
            
        Returns:
        --------
        List
            图表对象列表
        """
        return self.visualizer.create_analysis_report(
            analysis_results=analysis_results,
            save_path=save_path,
            factor_name=factor_name
        )
        
    def generate_report(self, analysis_results: Dict[str, Any],
                       output_path: str = "reports/factor_analysis_report.html",
                       factor_name: str = "Factor") -> str:
        """
        生成HTML分析报告
        
        Parameters:
        -----------
        analysis_results : Dict[str, Any]
            分析结果
        output_path : str, optional
            输出路径
        factor_name : str, optional
            因子名称
            
        Returns:
        --------
        str
            报告文件路径
        """
        try:
            # 确保目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 生成HTML报告
            html_content = self._generate_html_report(analysis_results, factor_name)
            
            # 保存报告
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            logger.info(f"Report generated: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            raise
            
    def register_factor(self, factor_class: type, **default_params) -> None:
        """
        注册新的因子类

        Parameters:
        -----------
        factor_class : type
            继承自BaseFactor的因子类
        **default_params : dict
            默认参数
        """
        self.factor_engine.register_factor(factor_class, **default_params)

    def list_factors(self):
        """
        列出所有已注册的因子

        Returns:
        --------
        List[str]
            已注册因子名称列表
        """
        return self.factor_engine.get_available_factors()
        
    def get_available_factors(self) -> List[str]:
        """
        获取可用的因子列表
        
        Returns:
        --------
        List[str]
            可用因子名称列表
        """
        return self.factor_engine.get_available_factors()
        
    def get_factor_info(self, factor_name: str) -> Dict[str, Any]:
        """
        获取因子详细信息
        
        Parameters:
        -----------
        factor_name : str
            因子名称
            
        Returns:
        --------
        Dict[str, Any]
            因子信息
        """
        return self.factor_engine.get_factor_info(factor_name)
        
    def clear_cache(self) -> None:
        """清空所有缓存"""
        self.data_adapter.clear_cache()
        self.factor_engine.clear_cache()
        logger.info("All caches cleared")
        
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
        --------
        Dict[str, Any]
            系统信息
        """
        return {
            'available_factors': self.get_available_factors(),
            'data_cache_info': self.data_adapter.get_cache_info(),
            'factor_cache_info': self.factor_engine.get_cache_info(),
            'performance_stats': self.factor_engine.get_performance_stats(),
            'config': self.config
        }

    def _load_config(self, config_path: str = None, **config_kwargs) -> Dict[str, Any]:
        """
        加载配置

        Parameters:
        -----------
        config_path : str, optional
            配置文件路径
        **config_kwargs : dict
            配置参数

        Returns:
        --------
        Dict[str, Any]
            配置字典
        """
        # 默认配置
        default_config = {
            'data_source': {
                'cache_enabled': True,
                'cache_size': 1000
            },
            'factor_calculation': {
                'parallel_enabled': False,
                'max_workers': 4,
                'default_lookback': 252,
                'min_periods': 20
            },
            'alphalens_settings': {
                'periods': [1, 5, 10, 20],
                'quantiles': 5,
                'max_loss': 0.35,
                'zero_aware': False
            },
            'visualization': {
                'figure_size': [12, 8],
                'dpi': 300,
                'style': 'seaborn',
                'save_format': 'png'
            }
        }

        # 从文件加载配置
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)

                # 合并配置
                config = self._merge_config(default_config, file_config)
                logger.info(f"Loaded config from: {config_path}")

            except Exception as e:
                logger.warning(f"Error loading config file {config_path}: {str(e)}")
                config = default_config
        else:
            config = default_config

        # 应用运行时参数
        if config_kwargs:
            config = self._merge_config(config, config_kwargs)

        return config

    def _merge_config(self, base_config: Dict, update_config: Dict) -> Dict:
        """
        递归合并配置字典

        Parameters:
        -----------
        base_config : Dict
            基础配置
        update_config : Dict
            更新配置

        Returns:
        --------
        Dict
            合并后的配置
        """
        result = base_config.copy()

        for key, value in update_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value

        return result

    def _generate_html_report(self, analysis_results: Dict[str, Any], factor_name: str) -> str:
        """
        生成HTML报告

        Parameters:
        -----------
        analysis_results : Dict[str, Any]
            分析结果
        factor_name : str
            因子名称

        Returns:
        --------
        str
            HTML内容
        """
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{factor_name} - 因子分析报告</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                h1 {{
                    color: #2c3e50;
                    text-align: center;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }}
                h2 {{
                    color: #34495e;
                    margin-top: 30px;
                }}
                .summary-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }}
                .summary-table th, .summary-table td {{
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: center;
                }}
                .summary-table th {{
                    background-color: #3498db;
                    color: white;
                }}
                .metric-card {{
                    display: inline-block;
                    background-color: #ecf0f1;
                    padding: 15px;
                    margin: 10px;
                    border-radius: 5px;
                    min-width: 150px;
                    text-align: center;
                }}
                .metric-value {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                }}
                .metric-label {{
                    font-size: 14px;
                    color: #7f8c8d;
                    margin-top: 5px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>{factor_name} 因子分析报告</h1>

                <h2>📊 核心指标概览</h2>
                {self._generate_summary_cards(analysis_results)}

                <h2>📈 IC分析</h2>
                {self._generate_ic_summary_table(analysis_results)}

                <h2>💰 分位数收益分析</h2>
                {self._generate_returns_summary_table(analysis_results)}

                <h2>🔄 换手率分析</h2>
                {self._generate_turnover_summary(analysis_results)}

                <h2>📋 详细统计</h2>
                {self._generate_detailed_stats(analysis_results)}

                <div style="margin-top: 40px; text-align: center; color: #7f8c8d;">
                    <p>报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>Powered by T_Trade Factor Research Framework</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_template

    def _generate_summary_cards(self, analysis_results: Dict[str, Any]) -> str:
        """生成汇总卡片"""
        cards_html = ""

        if 'summary_stats' in analysis_results:
            stats = analysis_results['summary_stats']

            # IC均值卡片
            if 'ic_mean' in stats:
                ic_mean = stats['ic_mean']
                if hasattr(ic_mean, 'mean'):
                    ic_value = ic_mean.mean()
                else:
                    ic_value = ic_mean
                cards_html += f"""
                <div class="metric-card">
                    <div class="metric-value">{ic_value:.4f}</div>
                    <div class="metric-label">IC均值</div>
                </div>
                """

            # IC信息比率卡片
            if 'ic_ir' in stats:
                ic_ir = stats['ic_ir']
                if hasattr(ic_ir, 'mean'):
                    ir_value = ic_ir.mean()
                else:
                    ir_value = ic_ir
                cards_html += f"""
                <div class="metric-card">
                    <div class="metric-value">{ir_value:.4f}</div>
                    <div class="metric-label">IC信息比率</div>
                </div>
                """

            # 多空收益卡片
            if 'spread_mean' in stats:
                spread_mean = stats['spread_mean']
                if hasattr(spread_mean, 'mean'):
                    spread_value = spread_mean.mean()
                else:
                    spread_value = spread_mean
                cards_html += f"""
                <div class="metric-card">
                    <div class="metric-value">{spread_value:.4f}</div>
                    <div class="metric-label">多空收益</div>
                </div>
                """

        return cards_html if cards_html else "<p>暂无汇总数据</p>"

    def _generate_ic_summary_table(self, analysis_results: Dict[str, Any]) -> str:
        """生成IC汇总表"""
        if 'ic_analysis' not in analysis_results or 'ic_summary' not in analysis_results['ic_analysis']:
            return "<p>暂无IC分析数据</p>"

        ic_summary = analysis_results['ic_analysis']['ic_summary']

        html = '<table class="summary-table">'
        html += '<tr><th>统计量</th>'

        # 表头
        for period in ic_summary.columns:
            html += f'<th>{period}期</th>'
        html += '</tr>'

        # 数据行
        for stat in ['mean', 'std', 'min', '25%', '50%', '75%', 'max']:
            if stat in ic_summary.index:
                html += f'<tr><td>{stat}</td>'
                for period in ic_summary.columns:
                    value = ic_summary.loc[stat, period]
                    html += f'<td>{value:.4f}</td>'
                html += '</tr>'

        html += '</table>'
        return html

    def _generate_returns_summary_table(self, analysis_results: Dict[str, Any]) -> str:
        """生成收益汇总表"""
        if 'quantile_returns' not in analysis_results or 'mean_returns_by_quantile' not in analysis_results['quantile_returns']:
            return "<p>暂无分位数收益数据</p>"

        returns_data = analysis_results['quantile_returns']['mean_returns_by_quantile']

        html = '<table class="summary-table">'
        html += '<tr><th>分位数</th>'

        # 表头
        periods = returns_data.columns.get_level_values(0).unique()
        for period in periods:
            html += f'<th>{period}期</th>'
        html += '</tr>'

        # 数据行
        for quantile in returns_data.index:
            html += f'<tr><td>Q{quantile}</td>'
            for period in periods:
                if period in returns_data.columns:
                    value = returns_data.loc[quantile, period]
                    html += f'<td>{value:.4f}</td>'
                else:
                    html += '<td>-</td>'
            html += '</tr>'

        html += '</table>'
        return html

    def _generate_turnover_summary(self, analysis_results: Dict[str, Any]) -> str:
        """生成换手率汇总"""
        if 'turnover_analysis' not in analysis_results or 'quantile_turnover' not in analysis_results['turnover_analysis']:
            return "<p>暂无换手率数据</p>"

        turnover_data = analysis_results['turnover_analysis']['quantile_turnover']
        avg_turnover = turnover_data.mean()

        html = '<table class="summary-table">'
        html += '<tr><th>持有期</th><th>平均换手率</th></tr>'

        for period in avg_turnover.index:
            html += f'<tr><td>{period}期</td><td>{avg_turnover[period]:.4f}</td></tr>'

        html += '</table>'
        return html

    def _generate_detailed_stats(self, analysis_results: Dict[str, Any]) -> str:
        """生成详细统计信息"""
        html = ""

        if 'summary_stats' in analysis_results:
            stats = analysis_results['summary_stats']

            html += '<table class="summary-table">'
            html += '<tr><th>指标</th><th>数值</th></tr>'

            for key, value in stats.items():
                if isinstance(value, (int, float)):
                    html += f'<tr><td>{key}</td><td>{value:.4f}</td></tr>'
                else:
                    html += f'<tr><td>{key}</td><td>{str(value)}</td></tr>'

            html += '</table>'
        else:
            html = "<p>暂无详细统计数据</p>"

        return html
