import os
import pandas as pd
import akshare as ak
from analyze.stock_analyze.stock_analyze import StockAnalyzer
from analyze.industry_analyze.industry_analyze import IndustryAnalyzer

class StockSelector:
    def __init__(self, DataGenerator):
        self.DataGenerator = DataGenerator

    def _ma_signal(self, date_range, code, period, window_size=7):
        ma_analyzer = StockAnalyzer(self.DataGenerator, code, date_range[0], date_range[1], period=period)
        crossover_date = ma_analyzer._ma_cross()
        if(crossover_date != None):
            open_window = (pd.to_datetime(date_range[1]) - pd.Timedelta(days=window_size)).date()
            if crossover_date >= open_window:
                return True
        return False

    def _macd_signal(self, date_range, code, period):
        pass

    def _price_low_signal(self, data_range, code, price_margin):
        pass

    def _sel_stock(self, date_range, industry):
        """扫描行业中股票，检测均线交叉信号"""
        os.makedirs('data/candidate', exist_ok=True)
        candidate_stocks = []

        # 获取行业股票列表
        stock_list = ak.stock_board_industry_cons_em(symbol=industry)['代码'].tolist()
        for stock in stock_list:
            if self._ma_signal(date_range, stock, 'weekly'):
                candidate_stocks.append(stock)

        if(len(candidate_stocks) != 0):
            # 保存候选股票信息
            candidate_df = pd.DataFrame(candidate_stocks, columns=['股票代码'])
            candidate_df.to_csv(f"data/candidate/{industry}_candidates.csv", index=False, encoding='utf_8_sig')

    def stock_candidate(self, date, industry_list):
        for industry in industry_list:
            self._sel_stock(date, industry)