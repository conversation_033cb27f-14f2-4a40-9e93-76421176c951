import pandas as pd
from common.config import SIM_TRADE_RESULT_DIR
import os
from bokeh.plotting import figure, output_file, save
from bokeh.models import ColumnDataSource, HoverTool
from bokeh.layouts import gridplot
import re

class TradePlot:
    def __init__(self, db_manage=None):
        self.db_manage = db_manage
        if SIM_TRADE_RESULT_DIR and not os.path.exists(SIM_TRADE_RESULT_DIR):
            os.makedirs(SIM_TRADE_RESULT_DIR)

    def get_bar_data(self, code, freq="daily", start_date=None, end_date=None):
        """
        从数据库获取指定标的的bar数据
        :param code: 股票代码
        :param freq: 周期，默认'daily'
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: DataFrame
        """
        if self.db_manage is None or not hasattr(self.db_manage, "hist_db"):
            raise ValueError("db_manage or db_manage.hist_db is not set")
        df = self.db_manage.hist_db.query_hist_data(
            category=None, freq=freq, code=code, start_date=start_date, end_date=end_date
        )
        return df

    def plot_kline_with_trades(self, code, trade_records=None, freq="daily", save_path=None):
        """
        获取bar数据并绘制K线和买卖点。
        K线图的显示范围会根据交易记录的日期自动调整，并前后扩展一段时间以提供上下文。
        :param code: 股票代码（用于数据查询和展示）
        :param trade_records: 交易记录list
        :param freq: 周期
        :param save_path: 保存html路径（可选）
        """
        start_date, end_date = None, None
        if trade_records:
            trade_dates = [pd.to_datetime(rec['date']) for rec in trade_records if rec.get('date')]
            if trade_dates:
                # 增加10天buffer
                start_date = (min(trade_dates) - pd.Timedelta(days=10)).strftime('%Y-%m-%d')
                end_date = (max(trade_dates) + pd.Timedelta(days=10)).strftime('%Y-%m-%d')

        bar_df = self.get_bar_data(code, freq, start_date=start_date, end_date=end_date)
        if bar_df is None or bar_df.empty:
            print("bar_df is empty")
            return

        df = bar_df.copy()
        try:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
        except Exception:
            print(f"date {df['date']} 转换失败")
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
        df = df.sort_values('date')
        # 跳过没有数据的日期（如节假日），只用实际有数据的行
        df = df[df['date'].notnull() & df['open'].notnull() & df['close'].notnull() & df['high'].notnull() & df['low'].notnull()]
        # 保证每个唯一日期只保留一条数据
        df = df.groupby('date', as_index=False).last()

        inc = df['close'] >= df['open']
        dec = df['close'] < df['open']
        # 以毫秒为单位的宽度，适合日线
        w = 20 * 60 * 60 * 1000

        source_inc = ColumnDataSource(df[inc])
        source_dec = ColumnDataSource(df[dec])
        source = ColumnDataSource(df)

        p = figure(
            x_axis_type="datetime",
            title=f"K线图 - {code}",
            width=1000,
            height=500,
            tools="pan,wheel_zoom,box_zoom,reset,save",
            x_axis_label="日期",
            sizing_mode="stretch_width"
        )
        seg = p.segment('date', 'high', 'date', 'low', color="black", source=source)
        p.vbar(x='date', width=w, top='close', bottom='open', fill_color="#FF3333", line_color="red", source=source_inc)
        p.vbar(x='date', width=w, top='close', bottom='open', fill_color="#33CC33", line_color="green", source=source_dec)

        # 买卖点
        if trade_records:
            # 打印部分交易记录用于调试
            buy_x, buy_y, sell_x, sell_y = [], [], [], []
            for rec in trade_records:
                action = rec.get('action')
                price = rec.get('price')
                date = rec.get('date')
                if not date or price is None:
                    continue
                # 转换date为datetime
                if not isinstance(date, pd.Timestamp):
                    try:
                        date = pd.to_datetime(date)
                    except Exception:
                        continue
                if action == 'buy':
                    buy_x.append(date)
                    buy_y.append(price)
                elif action == 'sell':
                    sell_x.append(date)
                    sell_y.append(price)
            if buy_x:
                p.scatter(buy_x, buy_y, marker="triangle", size=15, color="blue", legend_label="Buy")
            if sell_x:
                p.scatter(sell_x, sell_y, marker="inverted_triangle", size=15, color="orange", legend_label="Sell")
        else:
            print(f"[TradePlot] No trade records found for {code}")

        p.legend.location = "top_left"
        p.xaxis.axis_label = "交易序号"
        p.yaxis.axis_label = "价格"
        # 修正：只为segment添加hover，mode改为'mouse'，避免vline模式下多点重叠
        hover = HoverTool(
            tooltips=[
                ("日期", "@date{%F}"),
                ("开盘", "@open"),
                ("收盘", "@close"),
                ("最高", "@high"),
                ("最低", "@low"),
            ],
            formatters={'@date': 'datetime'},
            mode='mouse',
            renderers=[seg]
        )
        p.add_tools(hover)

        if save_path:
            output_file(save_path, title=f"K线图 - {code}")
            save(p)
            self.patch_html_for_qwebengine(save_path)  # 直接在此调用

    def plot_kline_with_trades_account(self, account_profile):
        trade_records_map = account_profile.get("trade_records", {})

        for item in account_profile.get("equity_pool", []):
            code = item.get("code")
            if not code:
                continue

            symbol_trade_records = trade_records_map.get(code, [])
            freq = item.get("freq", "daily")
            save_path = os.path.join(SIM_TRADE_RESULT_DIR, f"{code}_kline_with_trades.html")
            self.plot_kline_with_trades(
                code=code,
                trade_records=symbol_trade_records,
                freq=freq,
                save_path=save_path
            )
            print(f"Saved K线图和交易记录到: {save_path}")


    def plot_account_return_curve(self, snapshots_df, symbol=None, save_path=None):
        """
        绘制账户收益率曲线
        :param snapshots_df: DataFrame，包含至少['date','value']，如有'return_rate'则优先用
        :param symbol: 标题用
        :param save_path: 保存html路径（可选）
        """
        if snapshots_df is None or snapshots_df.empty:
            print("snapshots_df is empty")
            return
        df = snapshots_df.copy()
        # 过滤掉 'init' 日期，因为它不能被转换为datetime
        df = df[df['date'] != 'init']
        if df.empty:
            print("No valid date snapshots to plot after filtering 'init'.")
            return
        if not pd.api.types.is_datetime64_any_dtype(df['date']):
            df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        # 计算累计收益率
        if "return_rate" in df.columns and df["return_rate"].notnull().any():
            df["cum_return"] = df["return_rate"].cumsum()
        else:
            # 用账户总资产计算累计收益率
            start_value = df["value"].iloc[0]
            df["cum_return"] = (df["value"] - start_value) / start_value

        # account_return_curve部分也修正
        p = figure(x_axis_type="datetime", title=f"account_return_curve{f' - {symbol}' if symbol else ''}", width=1000, height=400, sizing_mode="stretch_width")
        p.line(df["date"], df["cum_return"], line_width=2, legend_label="total_return")
        p.circle(df["date"], df["cum_return"], size=5, color="navy", alpha=0.5)
        p.xaxis.axis_label = "date"
        p.yaxis.axis_label = "total_return"
        p.legend.location = "top_left"
        p.add_tools(HoverTool(
            tooltips=[
                ("日期", "@x{%F}"),
                ("累计收益", "@y"),
            ],
            formatters={'@x': 'datetime'},
            mode='vline'
        ))
        if save_path:
            output_file(save_path, title="account_return_curve")
            save(p)
            self.patch_html_for_qwebengine(save_path)  # 直接在此调用
        else:
            from bokeh.io import show
            show(p)

    @staticmethod
    def patch_html_for_qwebengine(file_path):
        """
        Injects a polyfill for String.prototype.replaceAll to support older QWebEngineView.
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            polyfill = """
        <script>
        if (!String.prototype.replaceAll) {
            String.prototype.replaceAll = function(search, replacement) {
                var target = this;
                return target.split(search).join(replacement);
            };
        }
        if (!Array.prototype.at) {
            Array.prototype.at = function(n) {
                n = Math.trunc(n) || 0;
                if (n < 0) n += this.length;
                if (n < 0 || n >= this.length) return undefined;
                return this[n];
            };
        }
        if (!String.prototype.at) {
            String.prototype.at = function(n) {
                n = Math.trunc(n) || 0;
                if (n < 0) n += this.length;
                if (n < 0 || n >= this.length) return undefined;
                return this.charAt(n);
            };
        }
        </script>
"""

            content, count = re.subn(r'(<head.*?>)', r'\1' + polyfill, content, count=1, flags=re.IGNORECASE | re.DOTALL)

            if count == 0:
                content = polyfill + content

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            print(f"Error patching HTML file {file_path}: {e}")

