"""
因子基类

定义所有因子的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
import logging

logger = logging.getLogger(__name__)


class BaseFactor(ABC):
    """
    因子基类，所有因子都应继承此类
    
    提供因子计算的标准接口和通用功能，包括:
    - 因子计算的抽象方法
    - 参数验证
    - 数据需求声明
    - 缓存支持
    - 错误处理
    """
    
    def __init__(self, name: str, description: str = "", category: str = "custom"):
        """
        初始化因子
        
        Parameters:
        -----------
        name : str
            因子名称，应该是唯一的标识符
        description : str, optional
            因子描述，用于文档和报告
        category : str, optional
            因子分类，如 'technical', 'fundamental', 'macro'
        """
        self.name = name
        self.description = description
        self.category = category
        self._cache = {}
        self._last_calculation_params = None
        
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算因子值
        
        Parameters:
        -----------
        data : pd.DataFrame
            输入数据，MultiIndex (date, asset)
            包含计算因子所需的所有字段
        **params : dict
            计算参数，具体参数由子类定义
            
        Returns:
        --------
        pd.Series
            因子值，MultiIndex (date, asset)
            
        Raises:
        -------
        ValueError
            当输入数据不符合要求时
        NotImplementedError
            子类必须实现此方法
        """
        pass
        
    @abstractmethod
    def get_required_data(self) -> List[str]:
        """
        获取计算因子所需的数据字段
        
        Returns:
        --------
        List[str]
            所需数据字段列表，如 ['close', 'volume', 'pe']
        """
        pass
        
    def validate_params(self, **params) -> bool:
        """
        验证计算参数
        
        Parameters:
        -----------
        **params : dict
            计算参数
            
        Returns:
        --------
        bool
            参数是否有效
        """
        # 默认实现：检查参数是否在默认参数中
        default_params = self.get_default_params()
        for key, value in params.items():
            if key in default_params:
                # 检查参数类型
                default_value = default_params[key]
                if not isinstance(value, type(default_value)):
                    logger.warning(f"Parameter {key} type mismatch: expected {type(default_value)}, got {type(value)}")
                    return False
        return True
        
    def get_default_params(self) -> Dict[str, Any]:
        """
        获取默认参数
        
        Returns:
        --------
        Dict[str, Any]
            默认参数字典
        """
        return {}
        
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据
        
        Parameters:
        -----------
        data : pd.DataFrame
            输入数据
            
        Returns:
        --------
        bool
            数据是否有效
        """
        # 检查数据格式
        if not isinstance(data.index, pd.MultiIndex):
            logger.error("Data must have MultiIndex (date, asset)")
            return False
            
        if data.index.nlevels != 2:
            logger.error("Data index must have exactly 2 levels (date, asset)")
            return False
            
        # 检查必需字段
        required_fields = self.get_required_data()
        missing_fields = [field for field in required_fields if field not in data.columns]
        if missing_fields:
            logger.error(f"Missing required fields: {missing_fields}")
            return False
            
        # 检查数据是否为空
        if data.empty:
            logger.error("Input data is empty")
            return False
            
        return True
        
    def calculate_with_validation(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        带验证的因子计算
        
        Parameters:
        -----------
        data : pd.DataFrame
            输入数据
        **params : dict
            计算参数
            
        Returns:
        --------
        pd.Series
            因子值
            
        Raises:
        -------
        ValueError
            当数据或参数验证失败时
        """
        # 验证数据
        if not self.validate_data(data):
            raise ValueError(f"Data validation failed for factor {self.name}")
            
        # 验证参数
        if not self.validate_params(**params):
            raise ValueError(f"Parameter validation failed for factor {self.name}")
            
        # 合并默认参数
        final_params = self.get_default_params()
        final_params.update(params)
        
        try:
            # 计算因子
            result = self.calculate(data, **final_params)
            
            # 验证结果
            if not isinstance(result, pd.Series):
                raise ValueError(f"Factor {self.name} must return pd.Series")
                
            # 检查结果索引
            if not isinstance(result.index, pd.MultiIndex):
                logger.warning(f"Factor {self.name} result should have MultiIndex")
                
            logger.info(f"Successfully calculated factor {self.name}")
            return result
            
        except Exception as e:
            logger.error(f"Error calculating factor {self.name}: {str(e)}")
            raise
            
    def get_info(self) -> Dict[str, Any]:
        """
        获取因子信息
        
        Returns:
        --------
        Dict[str, Any]
            因子信息字典
        """
        return {
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'required_data': self.get_required_data(),
            'default_params': self.get_default_params()
        }
        
    def __str__(self) -> str:
        """字符串表示"""
        return f"Factor(name='{self.name}', category='{self.category}')"
        
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"Factor(name='{self.name}', description='{self.description}', category='{self.category}')"


class FactorRegistry:
    """
    因子注册表，管理所有可用因子
    """
    
    def __init__(self):
        self._factors = {}
        
    def register(self, factor_class: type, **default_params) -> None:
        """
        注册因子类
        
        Parameters:
        -----------
        factor_class : type
            继承自BaseFactor的因子类
        **default_params : dict
            默认参数
        """
        if not issubclass(factor_class, BaseFactor):
            raise ValueError("Factor class must inherit from BaseFactor")
            
        try:
            factor_instance = factor_class(**default_params)
            self._factors[factor_instance.name] = {
                'class': factor_class,
                'instance': factor_instance,
                'default_params': default_params
            }
            logger.info(f"Registered factor: {factor_instance.name}")
        except Exception as e:
            logger.error(f"Failed to register factor {factor_class.__name__}: {str(e)}")
            raise
            
    def get_factor(self, name: str) -> BaseFactor:
        """
        获取因子实例
        
        Parameters:
        -----------
        name : str
            因子名称
            
        Returns:
        --------
        BaseFactor
            因子实例
        """
        if name not in self._factors:
            raise ValueError(f"Factor '{name}' not found. Available factors: {self.list_factors()}")
        return self._factors[name]['instance']
        
    def list_factors(self) -> List[str]:
        """
        列出所有可用因子
        
        Returns:
        --------
        List[str]
            因子名称列表
        """
        return list(self._factors.keys())
        
    def get_factors_by_category(self, category: str) -> List[str]:
        """
        按分类获取因子列表
        
        Parameters:
        -----------
        category : str
            因子分类
            
        Returns:
        --------
        List[str]
            该分类下的因子名称列表
        """
        return [name for name, info in self._factors.items() 
                if info['instance'].category == category]
                
    def get_factor_info(self, name: str) -> Dict[str, Any]:
        """
        获取因子详细信息
        
        Parameters:
        -----------
        name : str
            因子名称
            
        Returns:
        --------
        Dict[str, Any]
            因子信息
        """
        if name not in self._factors:
            raise ValueError(f"Factor '{name}' not found")
        return self._factors[name]['instance'].get_info()
        
    def clear(self) -> None:
        """清空注册表"""
        self._factors.clear()
        logger.info("Factor registry cleared")


# 全局因子注册表实例
factor_registry = FactorRegistry()
