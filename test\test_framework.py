#!/usr/bin/env python3
"""
因子研究框架测试脚本

验证框架的核心功能是否正常工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

from factor_research import FactorResearch
from factor_research.factors.momentum import MomentumFactor, RSIFactor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试数据"""
    # 创建日期范围
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    assets = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    # 创建随机价格数据
    np.random.seed(42)
    data = {}
    
    for asset in assets:
        # 生成随机游走价格
        returns = np.random.normal(0.001, 0.02, len(dates))
        prices = 100 * np.exp(np.cumsum(returns))
        
        # 生成成交量数据
        volumes = np.random.lognormal(15, 0.5, len(dates))
        
        asset_data = pd.DataFrame({
            'open': prices * (1 + np.random.normal(0, 0.005, len(dates))),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.01, len(dates)))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.01, len(dates)))),
            'close': prices,
            'volume': volumes
        }, index=dates)
        
        data[asset] = asset_data
    
    return data

def test_factor_calculation():
    """测试因子计算功能"""
    logger.info("Testing factor calculation...")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 初始化研究框架
    research = FactorResearch()
    
    # 注册因子
    research.register_factor(MomentumFactor, lookback_period=20, skip_period=1)
    research.register_factor(RSIFactor, period=14)
    
    # 计算动量因子
    assets = list(test_data.keys())
    factor_data = research.calculate_factor(
        'MomentumFactor',
        assets=assets,
        start_date='2023-02-01',
        end_date='2023-11-30'
    )
    
    logger.info(f"Momentum factor calculated: {len(factor_data)} observations")
    logger.info(f"Factor data sample:\n{factor_data.head()}")
    
    # 计算RSI因子
    rsi_data = research.calculate_factor(
        'RSIFactor',
        assets=assets,
        start_date='2023-02-01',
        end_date='2023-11-30'
    )
    
    logger.info(f"RSI factor calculated: {len(rsi_data)} observations")
    logger.info(f"RSI data sample:\n{rsi_data.head()}")
    
    return factor_data, rsi_data

def test_factor_analysis():
    """测试因子分析功能"""
    logger.info("Testing factor analysis...")
    
    # 计算因子
    factor_data, _ = test_factor_calculation()
    
    # 初始化研究框架
    research = FactorResearch()
    
    # 分析因子
    analysis_results = research.analyze_factor(
        factor_data,
        periods=[1, 5, 10],
        quantiles=5
    )
    
    logger.info("Factor analysis completed")
    logger.info(f"Analysis results keys: {list(analysis_results.keys())}")
    
    # 检查IC分析结果
    if 'ic_analysis' in analysis_results:
        ic_results = analysis_results['ic_analysis']
        logger.info(f"IC analysis keys: {list(ic_results.keys())}")
        
        if 'ic_time_series' in ic_results:
            ic_data = ic_results['ic_time_series']
            logger.info(f"IC time series shape: {ic_data.shape}")
            logger.info(f"IC statistics:\n{ic_data.describe()}")
    
    return analysis_results

def test_batch_analysis():
    """测试批量分析功能"""
    logger.info("Testing batch analysis...")
    
    # 初始化研究框架
    research = FactorResearch()
    
    # 注册因子
    research.register_factor(MomentumFactor, lookback_period=20)
    research.register_factor(RSIFactor, period=14)
    
    # 批量分析
    assets = ['AAPL', 'MSFT', 'GOOGL']
    results = research.batch_analyze_factors(
        factor_names=['MomentumFactor', 'RSIFactor'],
        assets=assets,
        start_date='2023-02-01',
        end_date='2023-11-30',
        periods=[1, 5],
        quantiles=3
    )
    
    logger.info("Batch analysis completed")
    logger.info(f"Batch results keys: {list(results.keys())}")
    
    for factor_name, factor_results in results.items():
        logger.info(f"{factor_name} analysis keys: {list(factor_results.keys())}")
    
    return results

def test_configuration():
    """测试配置功能"""
    logger.info("Testing configuration...")
    
    # 创建配置
    config_data = {
        'analysis': {
            'default_periods': [1, 5, 10, 20],
            'default_quantiles': 5,
            'max_loss': 0.35
        },
        'visualization': {
            'figure_size': [12, 8],
            'style': 'seaborn',
            'chinese_font': 'SimHei'
        }
    }
    
    # 使用配置初始化研究框架
    research = FactorResearch(config=config_data)
    
    # 验证配置
    periods = research.config.get('analysis.default_periods')
    quantiles = research.config.get('analysis.default_quantiles')
    
    logger.info(f"Default periods from config: {periods}")
    logger.info(f"Default quantiles from config: {quantiles}")
    
    return research

def main():
    """主测试函数"""
    logger.info("Starting factor research framework tests...")
    
    try:
        # 测试因子计算
        logger.info("\n" + "="*50)
        logger.info("TEST 1: Factor Calculation")
        logger.info("="*50)
        test_factor_calculation()
        
        # 测试因子分析
        logger.info("\n" + "="*50)
        logger.info("TEST 2: Factor Analysis")
        logger.info("="*50)
        test_factor_analysis()
        
        # 测试批量分析
        logger.info("\n" + "="*50)
        logger.info("TEST 3: Batch Analysis")
        logger.info("="*50)
        test_batch_analysis()
        
        # 测试配置
        logger.info("\n" + "="*50)
        logger.info("TEST 4: Configuration")
        logger.info("="*50)
        test_configuration()
        
        logger.info("\n" + "="*50)
        logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
