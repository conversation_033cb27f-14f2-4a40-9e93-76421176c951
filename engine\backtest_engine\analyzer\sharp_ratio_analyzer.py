import backtrader as bt
import numpy as np

class SharpeRatioAnalyzer(bt.Analyzer):
    def __init__(self):
        self.returns = []
        self.risk_free_rate = 0.03  # 假设的无风险利率
        self.periods_per_year = 252  # 每年的交易日数量

    def next(self):
        # 当前收益
        total_value = self.strategy.broker.getvalue()
        self.returns.append(total_value)

    def get_analysis(self):
        if len(self.returns) < 2:
            return {'sharpe_ratio': None}

        # 总体收益率
        start_value = self.returns[0]
        end_value = self.returns[-1] + self.strategy.broker.getcash()
        total_return = (end_value - start_value) / start_value

        # 年化收益率（按252交易日估算）
        periods = len(self.returns)
        annual_return = (1 + total_return) ** (self.periods_per_year / periods) - 1

        # 年化波动率
        daily_returns = [(self.returns[i] - self.returns[i-1]) / self.returns[i-1] for i in range(1, len(self.returns))]
        daily_stddev = np.std(daily_returns)
        annual_stddev = daily_stddev * (self.periods_per_year ** 0.5)

        # Sharpe比率
        sharpe_ratio = (annual_return - self.risk_free_rate) / annual_stddev if annual_stddev != 0 else None
        return sharpe_ratio