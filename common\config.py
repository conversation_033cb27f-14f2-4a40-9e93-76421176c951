from pathlib import Path

TOP_DIR = Path(__file__).parent.parent

ACCOUNT_DIR = TOP_DIR / "data" / "accounts"
STRATEGY_DIR = TOP_DIR  / "strategies"
DATA_BASE_DIR = TOP_DIR / "data"
SIM_TRADE_RESULT_DIR = TOP_DIR / "data" / "sim_trade_results"

# 周期和复权映射，供全局统一调用
PERIOD_MAP = {
    "日线": "daily",
    "周线": "weekly",
    "月线": "monthly",
    "daily": "daily",
    "weekly": "weekly",
    "monthly": "monthly"
}
ADJ_MAP = {
    "不复权": "none",
    "前复权": "qfq",
    "后复权": "hfq",
    "none": "none",
    "qfq": "qfq",
    "hfq": "hfq"
}