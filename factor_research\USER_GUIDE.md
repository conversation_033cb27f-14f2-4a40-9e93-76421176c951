# 因子研究框架用户指南

## 🚀 快速开始

### 安装依赖

```bash
pip install pandas numpy alphalens matplotlib seaborn pyyaml scipy
```

### 基础使用

```python
from factor_research import FactorResearch

# 初始化研究框架
research = FactorResearch(db_manage=your_db_manage)

# 计算因子
factor_data = research.calculate_factor(
    factor_name="momentum_20d",
    assets=["000001", "000002"],
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# 分析因子
results = research.analyze_factor(factor_data)

# 生成报告
research.generate_report(results, "reports/momentum_analysis.html")
```

## 📊 支持的因子类型

### 动量因子
- **MomentumFactor**: 价格动量因子
- **RSIFactor**: 相对强弱指数
- **PriceVolumeTrendFactor**: 价量趋势因子

### 自定义因子

```python
from factor_research.factors import BaseFactor

class MyCustomFactor(BaseFactor):
    def calculate(self, data, **params):
        # 实现你的因子计算逻辑
        return factor_series
    
    def get_required_data(self):
        return ['close', 'volume']

# 注册自定义因子
research.register_factor(MyCustomFactor)
```

## 🔧 配置管理

```python
from factor_research.utils.config import FactorConfig

# 创建配置
config = FactorConfig()

# 修改配置
config.set('alphalens_settings.periods', [1, 5, 10, 20])
config.set('visualization.dpi', 300)

# 保存配置
config.save_to_file('my_config.yaml')

# 使用配置初始化框架
research = FactorResearch(config_path='my_config.yaml')
```

## 📈 分析功能

### IC分析
- IC时序分析
- IC分布统计
- IC累计图表
- IC信息比率

### 分位数分析
- 分位数收益统计
- 多空收益分析
- 收益分布热力图
- 累计收益曲线

### 换手率分析
- 分位数换手率
- 因子自相关分析
- 换手率时序图

## 🎯 批量分析

```python
# 批量分析多个因子
results = research.batch_analyze_factors(
    factor_names=["momentum_20d", "rsi_14d"],
    assets=asset_list,
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# 因子比较
comparison = research.compare_factors(factor_data_dict)
```

## 📊 可视化

框架自动生成各种分析图表：
- IC时序图和分布图
- 分位数收益图
- 累计收益曲线
- 因子分布分析
- 因子比较雷达图

## 🔍 示例代码

查看 `examples/basic_usage.py` 获取完整示例。

## 📝 注意事项

1. 确保数据格式为MultiIndex (date, asset)
2. 因子计算需要足够的历史数据
3. 建议启用缓存以提高性能
4. 生产环境中注意内存使用
