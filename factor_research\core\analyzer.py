"""
因子分析器

基于alphalens实现因子有效性分析，包括IC分析、分层回测、风险调整收益等。
"""

from typing import Dict, List, Tuple, Optional, Any
import pandas as pd
import numpy as np
import logging
import warnings

# 抑制alphalens的警告信息
warnings.filterwarnings('ignore')

try:
    import alphalens as al
    ALPHALENS_AVAILABLE = True
except ImportError:
    ALPHALENS_AVAILABLE = False
    al = None
    logging.warning("Alphalens not available. Some analysis features will be limited.")

from .data_adapter import DataAdapter

logger = logging.getLogger(__name__)


class FactorAnalyzer:
    """
    因子分析器，基于alphalens进行因子有效性分析
    
    主要功能:
    - IC分析 (Information Coefficient)
    - 分位数分析
    - 换手率分析
    - 因子收益分析
    - 风险调整收益分析
    - 因子衰减分析
    """
    
    def __init__(self, data_adapter: DataAdapter):
        """
        初始化因子分析器
        
        Parameters:
        -----------
        data_adapter : DataAdapter
            数据适配器对象
        """
        self.data_adapter = data_adapter

        if not ALPHALENS_AVAILABLE:
            logger.warning("Alphalens not available. Using simplified analysis methods.")
        else:
            logger.info("Alphalens available. Full analysis features enabled.")
        
    def analyze_factor(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20],
                      quantiles: int = 5, max_loss: float = 0.35,
                      groupby_data: pd.Series = None) -> Dict[str, Any]:
        """
        完整的因子分析
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据，MultiIndex (date, asset)
        periods : List[int], optional
            持有期列表，默认 [1, 5, 10, 20]
        quantiles : int, optional
            分位数数量，默认5
        max_loss : float, optional
            最大数据丢失比例，默认0.35
        groupby_data : pd.Series, optional
            分组数据，如行业分类
            
        Returns:
        --------
        Dict[str, Any]
            分析结果字典，包含各种分析指标
        """
        try:
            logger.info(f"Starting factor analysis for factor: {factor_data.name}")
            
            # 准备数据
            factor_data_clean, pricing_data, groupby_data = self._prepare_data_for_alphalens(
                factor_data, periods, quantiles, max_loss, groupby_data
            )
            
            if factor_data_clean.empty:
                logger.warning("No valid data for factor analysis")
                return {}
            
            # 执行各种分析
            results = {}
            
            # 1. IC分析
            logger.info("Performing IC analysis")
            results['ic_analysis'] = self._ic_analysis(factor_data_clean)
            
            # 2. 分位数收益分析
            logger.info("Performing quantile returns analysis")
            results['quantile_returns'] = self._quantile_returns_analysis(factor_data_clean)
            
            # 3. 换手率分析
            logger.info("Performing turnover analysis")
            results['turnover_analysis'] = self._turnover_analysis(factor_data_clean)
            
            # 4. 因子收益分析
            logger.info("Performing factor returns analysis")
            results['factor_returns'] = self._factor_returns_analysis(factor_data_clean)
            
            # 5. 因子自相关分析
            logger.info("Performing factor autocorrelation analysis")
            results['factor_autocorr'] = self._factor_autocorr_analysis(factor_data_clean)
            
            # 6. 分组分析（如果有分组数据）
            if groupby_data is not None:
                logger.info("Performing group analysis")
                results['group_analysis'] = self._group_analysis(factor_data_clean, groupby_data)
            
            # 7. 汇总统计
            results['summary_stats'] = self._generate_summary_stats(results)
            
            logger.info("Factor analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Error in factor analysis: {str(e)}")
            raise
            
    def _prepare_data_for_alphalens(self, factor_data: pd.Series, periods: List[int],
                                  quantiles: int, max_loss: float,
                                  groupby_data: pd.Series = None) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series]:
        """
        为alphalens准备数据

        Returns:
        --------
        Tuple[pd.DataFrame, pd.DataFrame, pd.Series]
            (factor_data_clean, pricing_data, groupby_data)
        """
        try:
            # 获取价格数据
            assets = factor_data.index.get_level_values(1).unique().tolist()
            start_date = factor_data.index.get_level_values(0).min()
            end_date = factor_data.index.get_level_values(0).max()

            # 扩展结束日期以获取足够的前瞻收益数据
            extended_end_date = end_date + pd.Timedelta(days=max(periods) + 10)

            if self.data_adapter:
                pricing_data = self.data_adapter.get_price_data(
                    assets=assets,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=extended_end_date.strftime('%Y-%m-%d'),
                    fields=['close']
                )['close']

                # 获取分组数据
                if groupby_data is None:
                    try:
                        groupby_data = self.data_adapter.get_industry_classification(assets)
                    except:
                        groupby_data = None
            else:
                # 创建模拟价格数据
                pricing_data = self._create_mock_pricing_data(factor_data, periods)
                groupby_data = None

            if ALPHALENS_AVAILABLE and al is not None:
                # 使用alphalens清洗数据
                factor_data_clean = al.utils.get_clean_factor_and_forward_returns(
                    factor=factor_data,
                    prices=pricing_data,
                    groupby=groupby_data,
                    periods=periods,
                    quantiles=quantiles,
                    max_loss=max_loss
                )
            else:
                # 简化的数据清洗
                factor_data_clean = self._simple_clean_factor_data(
                    factor_data, pricing_data, periods, quantiles
                )

            return factor_data_clean, pricing_data, groupby_data

        except Exception as e:
            logger.error(f"Error preparing data for alphalens: {str(e)}")
            raise

    def _create_mock_pricing_data(self, factor_data: pd.Series, periods: List[int]) -> pd.DataFrame:
        """创建模拟价格数据"""
        dates = factor_data.index.get_level_values(0).unique()
        assets = factor_data.index.get_level_values(1).unique()

        # 扩展日期范围
        max_period = max(periods)
        extended_dates = pd.date_range(
            start=dates.min(),
            end=dates.max() + pd.Timedelta(days=max_period + 10),
            freq='D'
        )

        # 创建随机价格数据
        np.random.seed(42)
        pricing_data = pd.DataFrame(
            index=extended_dates,
            columns=assets
        )

        for asset in assets:
            # 生成随机游走价格
            returns = np.random.normal(0.001, 0.02, len(extended_dates))
            prices = 100 * np.exp(np.cumsum(returns))
            pricing_data[asset] = prices

        return pricing_data.stack()

    def _simple_clean_factor_data(self, factor_data: pd.Series, pricing_data: pd.Series,
                                 periods: List[int], quantiles: int) -> pd.DataFrame:
        """简化的因子数据清洗（不使用alphalens）"""
        # 创建基础的因子数据框架
        factor_df = factor_data.to_frame('factor')

        # 计算前瞻收益
        for period in periods:
            returns = pricing_data.groupby(level=1).pct_change(period).shift(-period)
            factor_df[f'{period}D'] = returns

        # 计算分位数
        factor_df['factor_quantile'] = factor_df.groupby(level=0)['factor'].transform(
            lambda x: pd.qcut(x, quantiles, labels=False, duplicates='drop') + 1
        )

        return factor_df.dropna()
            
    def _ic_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """IC分析"""
        try:
            if ALPHALENS_AVAILABLE and al is not None:
                # 使用alphalens进行IC分析
                ic = al.performance.factor_information_coefficient(factor_data_clean)
                ic_summary = al.performance.factor_information_coefficient(factor_data_clean).describe()
            else:
                # 简化的IC分析
                ic = self._calculate_simple_ic(factor_data_clean)
                ic_summary = ic.describe()

            return {
                'ic_time_series': ic,
                'ic_summary': ic_summary
            }

        except Exception as e:
            logger.error(f"Error in IC analysis: {str(e)}")
            return {}

    def _calculate_simple_ic(self, factor_data_clean: pd.DataFrame) -> pd.DataFrame:
        """简化的IC计算"""
        ic_results = {}

        # 获取因子列和收益列
        factor_col = 'factor'
        return_cols = [col for col in factor_data_clean.columns if col.endswith('D')]

        for return_col in return_cols:
            # 按日期分组计算IC
            daily_ic = factor_data_clean.groupby(level=0).apply(
                lambda x: x[factor_col].corr(x[return_col]) if len(x) > 1 else np.nan
            )
            ic_results[return_col] = daily_ic

        return pd.DataFrame(ic_results)
            
    def _quantile_returns_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """分位数收益分析"""
        try:
            if ALPHALENS_AVAILABLE and al is not None:
                # 使用alphalens进行分位数分析
                quantile_returns = al.performance.mean_return_by_quantile(factor_data_clean)
                quantile_returns_spread = al.performance.compute_mean_returns_spread(
                    quantile_returns[0], quantile_returns[1], upper_quant=5, lower_quant=1
                )
                return {
                    'mean_returns_by_quantile': quantile_returns[0],
                    'std_returns_by_quantile': quantile_returns[1],
                    'quantile_returns_spread': quantile_returns_spread
                }
            else:
                # 简化的分位数分析
                return self._calculate_simple_quantile_returns(factor_data_clean)

        except Exception as e:
            logger.error(f"Error in quantile returns analysis: {str(e)}")
            return {}

    def _calculate_simple_quantile_returns(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """简化的分位数收益计算"""
        results = {}
        return_cols = [col for col in factor_data_clean.columns if col.endswith('D')]

        for return_col in return_cols:
            # 按分位数分组计算平均收益
            quantile_returns = factor_data_clean.groupby('factor_quantile')[return_col].agg(['mean', 'std'])
            results[f'{return_col}_mean'] = quantile_returns['mean']
            results[f'{return_col}_std'] = quantile_returns['std']

        return {'mean_returns_by_quantile': pd.DataFrame(results)}
            
    def _turnover_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """换手率分析"""
        try:
            if ALPHALENS_AVAILABLE and al is not None:
                # 使用alphalens进行换手率分析
                quantile_turnover = al.performance.quantile_turnover(factor_data_clean)
                factor_rank_autocorr = al.performance.factor_rank_autocorrelation(factor_data_clean)
                return {
                    'quantile_turnover': quantile_turnover,
                    'factor_rank_autocorr': factor_rank_autocorr
                }
            else:
                # 简化的换手率分析
                return self._calculate_simple_turnover(factor_data_clean)

        except Exception as e:
            logger.error(f"Error in turnover analysis: {str(e)}")
            return {}

    def _calculate_simple_turnover(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """简化的换手率计算"""
        # 计算因子自相关作为换手率的代理指标
        factor_autocorr = factor_data_clean.groupby(level=1)['factor'].apply(
            lambda x: x.autocorr(lag=1) if len(x) > 1 else np.nan
        )

        return {
            'factor_autocorr': factor_autocorr.to_frame('autocorr_1d'),
            'turnover_proxy': (1 - factor_autocorr.abs()).to_frame('turnover_proxy')
        }
            
    def _factor_returns_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """因子收益分析"""
        try:
            if ALPHALENS_AVAILABLE and al is not None:
                # 使用alphalens进行因子收益分析
                factor_returns = al.performance.factor_returns(factor_data_clean)
                cumulative_returns = al.performance.cumulative_returns(factor_returns)
                return {
                    'factor_returns': factor_returns,
                    'cumulative_returns': cumulative_returns
                }
            else:
                # 简化的因子收益分析
                return self._calculate_simple_factor_returns(factor_data_clean)

        except Exception as e:
            logger.error(f"Error in factor returns analysis: {str(e)}")
            return {}

    def _calculate_simple_factor_returns(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """简化的因子收益计算"""
        results = {}
        return_cols = [col for col in factor_data_clean.columns if col.endswith('D')]

        for return_col in return_cols:
            # 计算多空组合收益（最高分位数 - 最低分位数）
            long_short_returns = factor_data_clean.groupby(level=0).apply(
                lambda x: x[x['factor_quantile'] == x['factor_quantile'].max()][return_col].mean() -
                         x[x['factor_quantile'] == x['factor_quantile'].min()][return_col].mean()
            )
            results[return_col] = long_short_returns

        factor_returns_df = pd.DataFrame(results)
        cumulative_returns = (1 + factor_returns_df).cumprod() - 1

        return {
            'factor_returns': factor_returns_df,
            'cumulative_returns': cumulative_returns
        }

    def _factor_autocorr_analysis(self, factor_data_clean: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """因子自相关分析"""
        try:
            if ALPHALENS_AVAILABLE and al is not None:
                # 使用alphalens进行自相关分析
                factor_rank_autocorr = al.performance.factor_rank_autocorrelation(factor_data_clean)
                return {'factor_rank_autocorr': factor_rank_autocorr}
            else:
                # 简化的自相关分析
                factor_autocorr = factor_data_clean.groupby(level=1)['factor'].apply(
                    lambda x: pd.Series([x.autocorr(lag=i) for i in range(1, 6)],
                                       index=[f'lag_{i}' for i in range(1, 6)])
                )
                return {'factor_autocorr': factor_autocorr.unstack()}

        except Exception as e:
            logger.error(f"Error in factor autocorrelation analysis: {str(e)}")
            return {}
            
    def _group_analysis(self, factor_data_clean: pd.DataFrame, groupby_data: pd.Series) -> Dict[str, pd.DataFrame]:
        """分组分析"""
        try:
            if ALPHALENS_AVAILABLE and al is not None:
                # 使用alphalens进行分组分析
                ic_by_group = al.performance.mean_information_coefficient(
                    factor_data_clean, by_group=True
                )
                returns_by_group = al.performance.mean_return_by_quantile(
                    factor_data_clean, by_group=True
                )
                return {
                    'ic_by_group': ic_by_group,
                    'returns_by_group': returns_by_group[0]  # 只取平均收益
                }
            else:
                # 简化的分组分析
                return {'group_analysis': pd.DataFrame({'note': ['Group analysis requires alphalens']})}

        except Exception as e:
            logger.error(f"Error in group analysis: {str(e)}")
            return {}
            
    def _generate_summary_stats(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成汇总统计"""
        try:
            summary = {}
            
            # IC统计
            if 'ic_analysis' in results and 'ic_time_series' in results['ic_analysis']:
                ic_data = results['ic_analysis']['ic_time_series']
                summary['ic_mean'] = ic_data.mean()
                summary['ic_std'] = ic_data.std()
                summary['ic_ir'] = ic_data.mean() / ic_data.std()  # Information Ratio
                summary['ic_positive_rate'] = (ic_data > 0).mean()
                
            # 分位数收益统计
            if 'quantile_returns' in results and 'quantile_returns_spread' in results['quantile_returns']:
                spread_data = results['quantile_returns']['quantile_returns_spread']
                summary['spread_mean'] = spread_data.mean()
                summary['spread_std'] = spread_data.std()
                summary['spread_sharpe'] = spread_data.mean() / spread_data.std()
                
            # 换手率统计
            if 'turnover_analysis' in results and 'quantile_turnover' in results['turnover_analysis']:
                turnover_data = results['turnover_analysis']['quantile_turnover']
                summary['avg_turnover'] = turnover_data.mean()
                
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary stats: {str(e)}")
            return {}
            
    def factor_decay_analysis(self, factor_data: pd.Series, periods: List[int] = None) -> Dict[str, pd.DataFrame]:
        """
        因子衰减分析
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据
        periods : List[int], optional
            分析的持有期列表，默认1-20天
            
        Returns:
        --------
        Dict[str, pd.DataFrame]
            衰减分析结果
        """
        if periods is None:
            periods = list(range(1, 21))  # 1-20天
            
        try:
            logger.info("Performing factor decay analysis")
            
            # 准备数据
            factor_data_clean, _, _ = self._prepare_data_for_alphalens(
                factor_data, periods, quantiles=5, max_loss=0.35
            )
            
            # 计算各期IC
            ic_decay = al.performance.factor_information_coefficient(factor_data_clean)
            
            # 计算各期收益
            returns_decay = al.performance.mean_return_by_quantile(factor_data_clean)[0]
            
            return {
                'ic_decay': ic_decay,
                'returns_decay': returns_decay
            }
            
        except Exception as e:
            logger.error(f"Error in factor decay analysis: {str(e)}")
            return {}

    def compare_factors(self, factor_data_dict: Dict[str, pd.Series],
                       periods: List[int] = [1, 5, 10, 20]) -> Dict[str, pd.DataFrame]:
        """
        比较多个因子的表现

        Parameters:
        -----------
        factor_data_dict : Dict[str, pd.Series]
            {factor_name: factor_data} 的字典
        periods : List[int], optional
            持有期列表

        Returns:
        --------
        Dict[str, pd.DataFrame]
            因子比较结果
        """
        try:
            logger.info(f"Comparing {len(factor_data_dict)} factors")

            comparison_results = {}

            # 分别分析每个因子
            factor_results = {}
            for factor_name, factor_data in factor_data_dict.items():
                logger.info(f"Analyzing factor: {factor_name}")
                results = self.analyze_factor(factor_data, periods=periods)
                factor_results[factor_name] = results

            # 汇总比较
            ic_comparison = pd.DataFrame()
            returns_comparison = pd.DataFrame()

            for factor_name, results in factor_results.items():
                if 'ic_analysis' in results and 'ic_time_series' in results['ic_analysis']:
                    ic_data = results['ic_analysis']['ic_time_series']
                    ic_comparison[factor_name] = ic_data.mean()

                if 'summary_stats' in results:
                    summary = results['summary_stats']
                    for key, value in summary.items():
                        if key not in comparison_results:
                            comparison_results[key] = {}
                        comparison_results[key][factor_name] = value

            comparison_results['ic_comparison'] = ic_comparison
            comparison_results['factor_results'] = factor_results

            return comparison_results

        except Exception as e:
            logger.error(f"Error comparing factors: {str(e)}")
            return {}
