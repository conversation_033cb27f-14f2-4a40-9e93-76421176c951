# GUI因子研究面板使用指南

## 概述

GUI因子研究面板为因子研究框架提供了图形化界面，让用户可以通过直观的界面进行因子计算、分析和可视化。

## 功能特性

### 🎯 核心功能
- **因子选择**: 支持动量因子、RSI因子、价量趋势因子
- **参数配置**: 图形化配置因子计算参数
- **股票池管理**: 灵活的股票代码输入
- **时间范围设置**: 日期选择器设置分析时间段
- **实时计算**: 后台线程计算，不阻塞界面
- **结果展示**: 多标签页展示分析结果
- **报告导出**: 一键导出HTML分析报告

### 📊 界面组件

#### 左侧控制面板
1. **因子选择组**
   - 因子类型下拉框
   - 动态参数配置区域

2. **股票池组**
   - 股票代码输入框
   - 支持逗号分隔的多股票输入

3. **时间范围组**
   - 开始日期选择器
   - 结束日期选择器

4. **操作按钮**
   - 计算因子按钮
   - 导出报告按钮
   - 进度条显示

#### 右侧结果面板
1. **分析结果标签页**
   - 因子统计信息
   - IC分析结果
   - 文本格式展示

2. **因子数据标签页**
   - 表格形式展示因子值
   - 支持大数据量显示

3. **可视化标签页**
   - 图表展示区域
   - 预留matplotlib集成

## 使用方法

### 1. 启动GUI
```python
from gui.mainframe import MainFrame
from database.db_manage import DBManage
from portfolio.account.account_manage import AccountManage

# 初始化系统
account_manage = AccountManage()
db_manage = DBManage(account_manage)

# 创建主窗口
main_window = MainFrame(
    position_manager=None,
    account_manage=account_manage,
    trade_engine=None,
    db_manage=db_manage,
    trade_ploter=None
)

main_window.show()
```

### 2. 使用因子研究面板

#### 步骤1: 选择因子
- 在"因子类型"下拉框中选择要分析的因子
- 根据因子类型调整相应参数

#### 步骤2: 配置参数
- **动量因子**: 设置回看期间和跳过期间
- **RSI因子**: 设置RSI计算周期
- **价量趋势因子**: 设置PVT计算周期

#### 步骤3: 选择股票池
- 在股票代码输入框中输入股票代码
- 多个股票用逗号分隔，如: `000001,000002,600000`

#### 步骤4: 设置时间范围
- 选择分析开始日期
- 选择分析结束日期

#### 步骤5: 执行计算
- 点击"计算因子"按钮
- 观察进度条显示计算进度
- 等待计算完成

#### 步骤6: 查看结果
- 在"分析结果"标签页查看统计信息
- 在"因子数据"标签页查看详细数据
- 在"可视化"标签页查看图表

#### 步骤7: 导出报告
- 点击"导出报告"按钮
- 选择保存位置
- 获得HTML格式的分析报告

## 支持的因子类型

### 1. 动量因子 (MomentumFactor)
- **描述**: 基于价格动量的技术因子
- **参数**:
  - 回看期间: 计算动量的历史天数 (1-252天)
  - 跳过期间: 跳过最近几天的数据 (0-10天)

### 2. RSI因子 (RSIFactor)
- **描述**: 相对强弱指数技术因子
- **参数**:
  - RSI周期: RSI计算周期 (2-100天)

### 3. 价量趋势因子 (PriceVolumeTrendFactor)
- **描述**: 结合价格和成交量的技术因子
- **参数**:
  - PVT周期: PVT计算周期 (1-100天)

## 技术架构

### 组件结构
```
PanelFactorResearch
├── FactorCalculationThread (计算线程)
├── Control Panel (控制面板)
│   ├── Factor Selection (因子选择)
│   ├── Stock Pool (股票池)
│   ├── Time Range (时间范围)
│   └── Action Buttons (操作按钮)
└── Result Panel (结果面板)
    ├── Analysis Results (分析结果)
    ├── Factor Data (因子数据)
    └── Visualization (可视化)
```

### 数据流
1. 用户输入参数 → GUI控件
2. GUI控件 → 参数验证
3. 参数验证 → 后台计算线程
4. 计算线程 → 因子研究框架
5. 因子研究框架 → 计算结果
6. 计算结果 → GUI结果展示

## 错误处理

### 常见错误及解决方案

1. **"因子研究框架不可用"**
   - 检查factor_research模块是否正确安装
   - 确认依赖包是否完整

2. **"请输入股票代码"**
   - 在股票代码输入框中输入有效的股票代码
   - 确保代码格式正确

3. **"因子计算失败"**
   - 检查股票代码是否存在
   - 确认时间范围内有足够的数据
   - 查看错误详情进行排查

## 扩展开发

### 添加新因子类型
1. 在factor_research框架中实现新因子类
2. 在`_update_factor_params`方法中添加参数界面
3. 在`_get_factor_params`方法中添加参数获取逻辑
4. 在因子下拉框中添加新选项

### 增强可视化
1. 集成matplotlib到可视化标签页
2. 添加交互式图表功能
3. 支持多种图表类型

### 性能优化
1. 实现结果缓存机制
2. 添加增量计算支持
3. 优化大数据量显示

## 注意事项

1. **数据依赖**: 需要确保数据库中有足够的历史数据
2. **计算时间**: 大股票池和长时间范围会增加计算时间
3. **内存使用**: 大量数据可能占用较多内存
4. **线程安全**: 计算过程中避免重复点击计算按钮

## 更新日志

### v1.0.0 (2024-12-27)
- 初始版本发布
- 支持三种基础因子类型
- 完整的GUI界面
- 基础分析和导出功能
