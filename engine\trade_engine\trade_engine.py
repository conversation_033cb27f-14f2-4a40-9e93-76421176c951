from engine.mail_engine.mail_engine import MailAgent
from common.config import PERIOD_MAP, SIM_TRADE_RESULT_DIR
from engine.trade_engine.trade_snapshot import TradeSnapshot
from engine.trade_engine.trade_analyzer import TradeAnalyzer
from engine.trade_engine.trade_broker import TradeBroker
from engine.indicator_engine.indicator_engine import IndicatorEngine
from strategies.trade_strategy.strategy_manager import StrategyManager
from portfolio.sizer.sizer_manager import SizerManager
import pandas as pd
import datetime
import time
import os
import csv
import shutil
import re


class TradeEngine:
    """
    只用pandas+talib计算最新数据点的技术指标和信号，不遍历历史数据，不依赖backtrader。
    由主控循环定时调用run_daily()，自行管理交易历史和持仓。
    """
    """
    建议策略类分开实现，理由如下：

    1. 回测引擎（如BacktestEngine）通常用backtrader等框架，策略需继承bt.Strategy，接口为next()。
    2. 交易/模拟盘引擎（如TradeEngine）一般用pandas+talib等，策略只需实现如calc_indicators(df)、generate_signal(df)等方法。
    3. 两者接口和生命周期不同，合并在一个class会导致代码混乱、难以维护。

    推荐做法：
    - 回测策略：继承bt.Strategy，专为backtrader设计。
    - 交易/模拟盘策略：单独写一个普通类，专为pandas/talib等设计。
    - 公共信号逻辑（如指标参数、信号函数）可抽象为工具函数或基类，供两者复用。

    这样结构清晰，易于扩展和维护。
    """
    

    def __init__(self, *args, **kw):
        self.position_manager = kw.get("position_manager")  # 推荐在此处实例化或传入
        self.db_manage = kw.get("db_manage")
        self.plotter = kw.get("trade_plot")  # 交易绘图器
        self.broker = kw.get("trade_broker")
        self.mail_agent = MailAgent()  # 邮件通知代理
        self.snapshot = TradeSnapshot(self.db_manage)  # 快照管理器
        self.analyzer = TradeAnalyzer()
        self.indicator_map = None  # 新增：缓存交易指标映射
        self.symbol_strategy_map = None
        self.account_profile = None  # 新增：缓存账户详细数据
        self._stop_flag = False
        # sizer_manager 应该在此处实例化
        self.sizer_manager = SizerManager(mode='trade', position_manager=self.position_manager)  # 推荐在这里实例化或赋值

    def trade_indicator_map(self, equity_pool=None):
        """
        根据账户配置为每个标的绑定唯一的indicator实例（支持参数去重）
        """
        default_indicator_cls = IndicatorEngine
        default_params = {}
        # 组装时将每个标的的params（如indicators参数）也加入indicator_map，便于后续调用
        indicator_map = IndicatorEngine.assemble_indicator_map(
            equity_pool=equity_pool,
            default_indicator_cls=default_indicator_cls,
            default_params=default_params
        )
        # 将每个标的的params配置也存入indicator_map，便于后续指标计算时获取
        for item in equity_pool or []:
            symbol = item["code"] if isinstance(item, dict) else str(item)
            params = item.get("indicator", {}).get("params", {})
            if symbol in indicator_map:
                indicator_map[symbol].params = params
        self.indicator_map = indicator_map
        return self.indicator_map

    def trade_strategy_map(self, equity_pool, sizer_map, indicator_map, position_manager, broker, default_strategy_name=None, default_params=None):
        """
        根据股票池配置为每个标的绑定唯一的策略实例（支持参数去重）
        """
        symbol_strategy_map = StrategyManager.assemble_strategy_map(
            equity_pool=equity_pool,
            mode='trade',
            default_strategy_name=default_strategy_name,
            default_params=default_params,
            sizer_map=sizer_map,
            indicator_map=indicator_map,
            position_manager=position_manager,
            broker=broker
        )
        return symbol_strategy_map

    def trade_sizer_map(self, equity_pool):
        """
        根据账户配置组装并返回每个标的的sizer实例（支持多种配置去重）
        """
        symbol_sizer_map = self.sizer_manager.assemble(
            equity_pool=equity_pool
        )
        return symbol_sizer_map

    def trade_map_init(self):
        """
        初始化交易映射，组装sizer、indicator和strategy映射
        """
        # 优先使用缓存的account_profile
        account_profile = self.account_profile
        if not account_profile:
            raise ValueError("Account not found.")
        
        equity_pool = account_profile.get("equity_pool", [])
        if not equity_pool:
            raise ValueError("No equity pool found in account profile.")

        # 组装sizer、indicator和strategy映射
        sizer_map = self.trade_sizer_map(equity_pool)
        indicator_map = self.trade_indicator_map(equity_pool)
        position_manager = self.position_manager
        broker = self.broker

        # 组装策略映射
        self.symbol_strategy_map = self.trade_strategy_map(
            equity_pool, sizer_map, indicator_map, position_manager, broker,
            default_strategy_name=account_profile.get("default_strategy_name"),
            default_params=account_profile.get("default_strategy_params")
        )

    def create_hist_cache(self, equity_pool):
        """
        创建历史数据缓存，供策略实例使用
        :param equity_pool: 股票池列表，每项为dict，需包含code
        """
        if not hasattr(self, "symbol_strategy_map"):
            raise ValueError("Symbol strategy map not initialized.")
        
        for item in equity_pool:
            symbol = item["code"] if isinstance(item, dict) else str(item)
            if symbol in self.symbol_strategy_map:
                strategy = self.symbol_strategy_map[symbol]
                # 初始化历史数据缓存
                if not hasattr(strategy, "_hist_df"):
                    hist_df = self.db_manage.hist_db.query_hist_data(
                        category="etf" if self.db_manage.data_engine.is_etf_code(symbol) else "stock",
                        freq=self.account_profile.get("period", "daily"),
                        code=symbol
                    )
                    strategy._hist_df = hist_df if hist_df is not None else None

    def update_hist_cache(self, strategy, latest_data, max_cache_len=200):
        """
        将最新bar数据拼接到对应策略实例的历史数据缓存，只保留最近max_cache_len条。
        若最新bar日期与缓存最后一行相同则替换，否则追加。
        :param latest_data: {symbol: bar数据}，但这里只处理一个symbol
        :param max_cache_len: 历史缓存最大长度
        """
        symbol, bar = next(iter(latest_data.items()))

        if bar is not None and strategy and hasattr(strategy, "_hist_df") and strategy._hist_df is not None:
            bar_df = pd.DataFrame([bar])
            df = strategy._hist_df
            bar_date = pd.to_datetime(bar.get("date"))
            # 检查缓存是否为空
            if len(df) > 0 and "date" in df.columns:
                last_date = pd.to_datetime(df.iloc[-1]["date"])
                if last_date == bar_date:
                    # 替换最后一行
                    df.iloc[-1] = bar_df.iloc[0].values
                else:
                    # 追加新bar
                    df = pd.concat([df, bar_df], ignore_index=True)
            else:
                # 缓存为空或无date列，直接追加
                df = pd.concat([df, bar_df], ignore_index=True)
            # 只保留最近max_cache_len条
            if len(df) > max_cache_len:
                df = df.iloc[-max_cache_len:].reset_index(drop=True)
            strategy._hist_df = df
        else:
            raise ValueError(f"Strategy {strategy} does not have a valid _hist_df or latest_data is empty for symbol {symbol}.")

    def before_trade(self, account_profile):
        self.account_profile = account_profile
        self.trade_map_init()
        self.db_manage.hist_db.update_indicator_data(self.indicator_map, period=account_profile.get("period", "daily"))
        self.create_hist_cache(account_profile.get("equity_pool", []))
        if(account_profile.get("name") == "test"):
            self.broker.reset(100000)  # 测试账户默认10万起始资金
            print(f"模拟交易账户初始化: {account_profile}")
        else:
            self.broker.load_account_info(account_profile)

    def update_realtime_data(self):
        """
        获取股票池所有标的的最新bar数据，返回{symbol: latest_row}，并更新broker的price_map
        """
        account_profile = self.account_profile
        if not account_profile:
            raise ValueError("Account not found.")
        equity_pool = account_profile.get("equity_pool", [])
        latest_data, price_map = self.db_manage.realtime_db.get_latest_data_map(equity_pool)
        print(f"latest_data: {latest_data} price_map: {price_map}")           
        return latest_data, price_map
        

    def run_trade(self, latest_data=None, debug=False):
        """
        1. 更新最新bar数据（可传入用于测试/回测）
        2. 运行strategy_map对应标的的on_bar执行交易
        """
        if latest_data is None:
            (latest_data, price_map) = self.update_realtime_data()
        else:
            # 如果传入了latest_data，则从中提取价格映射
            price_map = {symbol: bar.get("close") for symbol, bar in latest_data.items() if bar is not None}

        self.broker.update_price_map(price_map)

        for symbol, strategy in self.symbol_strategy_map.items():
            if not hasattr(strategy, "on_bar"):
                raise ValueError(f"Strategy for {symbol} does not implement on_bar method.")
            bar = latest_data.get(symbol)
            if bar is not None:
                # 只拼接当前symbol的bar到历史缓存
                self.update_hist_cache(strategy, {symbol: bar})
                strategy.on_bar(bar, debug=debug)

    def _update_account_profile(self):
        """
        从 broker 获取最新账户资金、持仓等信息，更新 self.account_profile
        """
        if not self.broker:
            raise ValueError("Broker not initialized.")
        if not self.account_profile:
            raise ValueError("Account profile not initialized.")

        # 获取资金信息
        cash = self.broker.get_cash()
        # 获取所有持仓
        positions = self.broker.positions if hasattr(self.broker, "positions") else {}
        # 计算总市值
        total_market_value = self.broker._total_market_value()
        
        # 可用资金
        available_cash = self.broker.get_available_cash() if hasattr(self.broker, "get_available_cash") else cash
        # 总资产
        total_assets = cash + total_market_value

        # 更新 account_profile
        self.account_profile["total_assets"] = total_assets
        self.account_profile["cash"] = cash
        self.account_profile["total_market_value"] = total_market_value
        self.account_profile["positions"] = positions
        self.account_profile["available_cash"] = available_cash
        if hasattr(self.broker, "price_map"):
            self.account_profile["price_map"] = self.broker.price_map
        # 汇总所有策略的trade_records
        trade_records = {}
        if self.symbol_strategy_map:
            for symbol, strategy in self.symbol_strategy_map.items():
                trade_records[symbol] = getattr(strategy, "trade_records", [])
        self.account_profile["trade_records"] = trade_records
    def _update_snapshot(self, date):
        # 获取账户总资产
        total_value = self.account_profile.get("cash", 0) + self.account_profile.get("total_market_value", 0)
        
        extra_json = {
            "positions": self.account_profile.get("positions", {}),
            "available_cash": self.account_profile.get("available_cash", 0)
        }
        self.snapshot.save_snapshot(
            account_id=self.account_profile.get("name"),
            value=total_value,
            date=date,
            extra_json=extra_json
        )

    def _plot_position_chart(self):
        """
        绘制当前持仓的图表
        """
        if not self.plotter:
            raise ValueError("Trade plotter not initialized.")
        
        positions = self.account_profile.get("positions", {})
        if not positions:
            return
        
        for symbol, position in positions.items():
            if "quantity" in position and position["quantity"] > 0:
                save_path = os.path.join(SIM_TRADE_RESULT_DIR, f"{symbol}_kline_with_trades.html")
                self.plotter.plot_kline_with_trades(
                    symbol, 
                    trade_records=self.symbol_strategy_map[symbol].trade_records,
                    freq=self.account_profile.get("period", "daily"),
                    save_path=save_path
                )

    def _plot_return_curve(self):
        snapshots_df = self.snapshot.get_snapshots(
            account_id=self.account_profile.get("name")
        )
        if snapshots_df is not None and not snapshots_df.empty and len(snapshots_df) > 1:
            save_path = os.path.join(
                SIM_TRADE_RESULT_DIR, 
                f"{self.account_profile.get('name')}_return_curve.html"
            )

            self.plotter.plot_account_return_curve(
                snapshots_df, 
                save_path=save_path
            )

    def save_trade_records_to_csv(self, trade_records):
        """
        将trade_records写入到SIM_TRADE_RESULT_DIR下的csv文件
        """
        os.makedirs(SIM_TRADE_RESULT_DIR, exist_ok=True)
        for symbol, records in trade_records.items():
            if not records:
                continue
            csv_path = os.path.join(SIM_TRADE_RESULT_DIR, f"{symbol}_trade_records.csv")
            if isinstance(records, pd.DataFrame):
                df = records
            else:
                df = pd.DataFrame(records)
            if not df.empty:
                df.to_csv(csv_path, index=False, encoding="utf-8-sig")

    def after_trade(self, date=datetime.date.today()):
        """
        交易结束后执行的逻辑
        1. 更新账户信息
        2. 更新快照
        3. 绘制交易结果图表
        4. 绘制收益率曲线
        5. （可选）写回当日指标到数据库
        6. （推荐）写回账户数据到数据库（如有账户管理/持久化需求）
        7. 写回trade_records到SIM_TRADE_RESULT_DIR下的csv文件
        """
        self._update_account_profile()
        self._update_snapshot(date)
        # 可选：当日交易结束后批量写回指标到数据库，保证数据持久化和后续分析
        self.db_manage.hist_db.update_indicator_data(
            self.indicator_map,
            period=self.account_profile.get("period", "daily")
        )
        # 推荐：在trade_engine中统一写回账户数据，保证业务流程完整
        self.db_manage.account_manage._save_account(self.account_profile)

    def plot_trade_result(self):
        self._plot_position_chart()
        self._plot_return_curve()

    def run_sim_trade(self, end_time = "15:00", window_min=15):
        """
        模拟交易主循环，定时执行交易逻辑。
        :param end_time: 交易结束时间，格式为"HH:MM"
        :param window_min: 每次运行的时间窗口（分钟）
        """
        self.before_trade()
        end_hour, end_minute = map(int, end_time.split(":"))
        end_time = datetime.time(end_hour, end_minute)

        while not self._stop_flag:
            now = datetime.datetime.now()
            if now.time() >= end_time:
                break

            # 执行交易逻辑
            self.run_trade()

            # 等待下一个时间窗口
            time.sleep(window_min * 60)

        self.after_trade()        

    def test_sim_trade_with_hist(self, n=None):
        """
        利用历史数据逐bar推进，模拟实时交易场景，仅用于开发阶段功能测试。
        支持多标的场景：每个bar的latest_data为{symbol: bar数据}，可同时驱动多个标的的策略。
        :param n: 回测天数（从历史数据开头起的前n个交易日），如未指定则测试整个数据集
        """
        if hasattr(self.db_manage, "hist_db") and hasattr(self.db_manage.hist_db, "get_hist_bar_map"):
            hist_db = self.db_manage.hist_db
            # 获取股票池所有标的
            equity_pool = self.account_profile.get("equity_pool", [])
            symbol_category_map = {}
            # 判断每个标的是ETF还是股票
            for item in equity_pool:
                code = item.get("code") if isinstance(item, dict) else str(item)
                symbol_category_map[code] = "etf" if self.db_manage.data_engine.is_etf_code(code) else "stock"
            
            bar_iter = hist_db.get_hist_bar_map(n, symbol_category_map=symbol_category_map)
            for bar_info in bar_iter:
                date = bar_info.get("date")
                latest_data = bar_info.get("latest_data")
                self.run_trade(latest_data=latest_data, debug=False)
                self.after_trade(date)
            # 写入所有trade_records到SIM_TRADE_RESULT_DIR下的csv文件
            trade_records = self.account_profile.get("trade_records", {})
            if trade_records:
                self.save_trade_records_to_csv(trade_records)
            self.plot_trade_result()
            print(f"模拟交易测试完成。")
        else:
            print("历史数据库不支持逐bar推进接口")

