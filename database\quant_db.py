import duckdb
import pandas as pd
import os
from datetime import datetime
from common.config import DATA_BASE_DIR  # 新增导入

class QuantDB:
    def __init__(self, db_path=None):
        # 数据库文件默认放在DATA_BASE_DIR
        if db_path is None:
            db_path = os.path.join(DATA_BASE_DIR, "quant_data.duckdb")
        self.db_path = db_path
        self.conn = duckdb.connect(database=db_path, read_only=False)

    def get_table_name(self, category, freq):
        """
        生成标准表名，如: stock_daily, indexfuture_240min, industry_weekly, market_monthly
        category: 'stock'/'indexfuture'/'industry'/'market'
        freq: '240min'/'daily'/'weekly'/'monthly'
        """
        return f"{category.lower()}_{freq.lower()}"

    def save_data(self, category, freq, df: pd.DataFrame, if_exists="replace"):
        """
        保存数据到指定分类和周期的表
        """
        table_name = self.get_table_name(category, freq)
        self.create_table(table_name, df, if_exists=if_exists)

    def load_data(self, category, freq, where=None):
        """
        读取指定分类和周期的数据
        """
        table_name = self.get_table_name(category, freq)
        return self.read_table(table_name, where=where)

    def create_table(self, table_name, df: pd.DataFrame, if_exists="replace"):
        """
        用DataFrame结构创建表，自动推断字段类型。
        if_exists: 'replace'/'append'/'fail'
        """
        if if_exists == "replace":
            self.conn.execute(f"DROP TABLE IF EXISTS {table_name}")
        self.conn.register("tmp_df", df)
        if_exists_sql = "OR REPLACE" if if_exists == "replace" else ""
        self.conn.execute(f"CREATE {if_exists_sql} TABLE {table_name} AS SELECT * FROM tmp_df")
        self.conn.unregister("tmp_df")

    def insert_df(self, table_name, df: pd.DataFrame):
        """向已存在表追加数据"""
        self.conn.register("tmp_df", df)
        self.conn.execute(f"INSERT INTO {table_name} SELECT * FROM tmp_df")
        self.conn.unregister("tmp_df")

    def query(self, sql: str, params=None) -> pd.DataFrame:
        """执行SQL查询，返回DataFrame"""
        return self.conn.execute(sql, params or ()).df()

    def read_table(self, table_name, where=None) -> pd.DataFrame:
        """读取整张表或带条件的表"""
        sql = f"SELECT * FROM {table_name}"
        if where:
            sql += f" WHERE {where}"
        return self.conn.execute(sql).df()

    def upsert_df(self, table_name, df: pd.DataFrame, key_cols):
        """
        基于主键列upsert数据（duckdb 0.9+支持merge语法）
        """
        # 先插入到临时表
        self.conn.register("tmp_df", df)
        merge_keys = " AND ".join([f"t.{k}=s.{k}" for k in key_cols])
        update_set = ", ".join([f"{col}=s.{col}" for col in df.columns if col not in key_cols])
        insert_cols = ", ".join(df.columns)
        insert_vals = ", ".join([f"s.{col}" for col in df.columns])
        sql = f"""
        MERGE INTO {table_name} t
        USING tmp_df s
        ON {merge_keys}
        WHEN MATCHED THEN UPDATE SET {update_set}
        WHEN NOT MATCHED THEN INSERT ({insert_cols}) VALUES ({insert_vals})
        """
        self.conn.execute(sql)
        self.conn.unregister("tmp_df")

    def close(self):
        self.conn.close()

