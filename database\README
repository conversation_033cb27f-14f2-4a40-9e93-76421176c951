personal trade analysis tool## DuckDB 常用操作

- 查看所有表名：
  ```sql
  SHOW TABLES;
  ```

- 查看表结构（字段名、类型等）：
  ```sql
  PRAGMA table_info('表名');
  ```

- 查看表的所有内容（预览数据）：
  ```sql
  SELECT * FROM 表名 LIMIT 10;
  ```

## account 数据库表结构说明

| 字段名      | 类型     | 非空   | 默认值 | 主键   | 说明           |
| ----------- | -------- | ------ | ------ | ------ | -------------- |
| account_id  | VARCHAR  | 是     | NULL   | 是     | 账户唯一标识   |
| info_json   | VARCHAR  | 否     | NULL   | 否     | 账户信息(JSON) |

# quant_data 数据库表结构说明
quant_data 数据库用于存储行情及相关数据，表格分为两类：

- **历史类数据表**：以 `daily`、`weekly`、`monthly` 结尾，存储历史K线数据和策略中常用的技术指标数据。
  包括 `stock_daily`、`stock_weekly`、`stock_monthly` 等。
- **实时数据表**：如 `etf_realtime`、`stock_realtime`，存储最新的实时行情数据，
  包括 ETF 和股票的实时行情。

包含以下主要表：

| 表名           | 类型     | 说明           |
| -------------- | -------- | -------------- |
| etf_realtime   | 实时数据 | ETF 实时行情   |
| stock_daily    | 历史数据 | 股票日线数据   |
| stock_monthly  | 历史数据 | 股票月线数据   |
| stock_realtime | 实时数据 | 股票实时行情   |
| stock_weekly   | 历史数据 | 股票周线数据   |

## stock_daily 表结构

| 字段名             | 类型    | 说明                |
| ------------------ | ------- | ------------------- |
| date               | VARCHAR | 交易日期            |
| open               | DOUBLE  | 开盘价              |
| close              | DOUBLE  | 收盘价              |
| high               | DOUBLE  | 最高价              |
| low                | DOUBLE  | 最低价              |
| volume             | BIGINT  | 成交量              |
| amount             | DOUBLE  | 成交额              |
| amplitude          | DOUBLE  | 振幅                |
| change             | DOUBLE  | 涨跌幅              |
| change_amount      | DOUBLE  | 涨跌额              |
| turnover_rate      | DOUBLE  | 换手率              |
| code               | VARCHAR | 股票代码            |
| boll_upper_15_2    | DOUBLE  | BOLL上轨(15,2)      |
| boll_middle_15_2   | DOUBLE  | BOLL中轨(15,2)      |
| boll_lower_15_2    | DOUBLE  | BOLL下轨(15,2)      |
| vol_ma_5           | DOUBLE  | 5日均量             |
| boll_upper_30_3    | DOUBLE  | BOLL上轨(30,3)      |
| boll_middle_30_3   | DOUBLE  | BOLL中轨(30,3)      |
| boll_lower_30_3    | DOUBLE  | BOLL下轨(30,3)      |
| vol_ma_10          | DOUBLE  | 10日均量            |

## etf_realtime 表结构

| 字段名           | 类型    | 说明           |
| ---------------- | ------- | -------------- |
| code             | VARCHAR | 证券代码       |
| name             | VARCHAR | 名称           |
| latest_price     | DOUBLE  | 最新价格       |
| volumn           | DOUBLE  | 成交量         |
| amount           | DOUBLE  | 成交额         |
| date             | VARCHAR | 日期           |
| change           | DOUBLE  | 涨跌幅         |
| open             | DOUBLE  | 开盘价         |
| high             | DOUBLE  | 最高价         |
| low              | DOUBLE  | 最低价         |
| previous_close   | DOUBLE  | 昨收价         |
| turnover_rate    | DOUBLE  | 换手率         |

# snapshots 数据库表结构说明

snapshots 数据库用于存储用于记录账户在某日的净值等信息：

| 字段名      | 类型    | 说明             |
| ----------- | ------- | ---------------- |
| account_id  | VARCHAR | 账户唯一标识     |
| date        | VARCHAR | 快照日期         |
| value       | DOUBLE  | 账户净值/资产等  |
| extra_json  | VARCHAR | 其他信息(JSON)   |
