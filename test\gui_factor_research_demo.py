"""
GUI因子研究演示

展示如何在图形界面中使用因子研究功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.mainframe import MainFrame
from database.db_manage import DBManage
from portfolio.account.account_manage import AccountManage


def main():
    """主函数"""
    print("启动GUI因子研究演示...")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    try:
        # 初始化系统组件
        print("初始化系统组件...")
        account_manage = AccountManage()
        db_manage = DBManage(account_manage)
        
        # 创建主窗口
        print("创建主窗口...")
        main_window = MainFrame(
            position_manager=None,
            account_manage=account_manage,
            trade_engine=None,
            db_manage=db_manage,
            trade_ploter=None
        )
        
        # 显示窗口
        main_window.show()
        print("GUI启动成功！")
        print("\n使用说明:")
        print("1. 点击 'Factor Research' 标签页")
        print("2. 选择因子类型和参数")
        print("3. 输入股票代码")
        print("4. 设置时间范围")
        print("5. 点击 '计算因子' 开始分析")
        print("6. 查看分析结果和可视化")
        print("7. 导出HTML报告")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
