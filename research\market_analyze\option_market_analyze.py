import akshare as ak
import pandas as pd
from data_gen.data_generator_em import DataGeneratorEM
from cal_lib.cal_lib import CalLib
from datetime import datetime, timedelta

class OptionAnalyze:
    def __init__(self):
        self.generator = DataGeneratorEM('2025-01-01')

    def _load_data(self, code):
        option = self.generator._read_csv(f"data/market/option/{code}.csv")
        return option

    def _month_sel(self, cur_month):
        near_month = (cur_month + timedelta(days=32)).replace(day=1)
        if 1 <= cur_month.month <= 3:
            far_month = cur_month.replace(month=6)
        elif 4 <= cur_month.month <= 6:
            far_month = cur_month.replace(month=9)
        elif 7 <= cur_month.month <= 9:
            far_month = cur_month.replace(month=12)
        elif 10 <= cur_month.month <= 12:
            far_month = cur_month.replace(year=cur_month.year + 1, month=3)

        # print("cur_month:", cur_month, "near_month:", near_month, "far_month:", far_month)
        return near_month, far_month

    def _cur_code_sel(self, code):
        if code == "IH":
            """上证50股指期货"""
            cur_code = "sh000016"
            title = "上证50"
        elif code == "IF":
            """沪深300股指期货"""
            cur_code = "sh000300"
            title = "沪深300"
        elif code == "IC":
            """中证500股指期货"""
            cur_code = "sh000905"
            title = "中证500"
        elif code == "IM":
            """中证1000股指期货"""
            cur_code = "sh000852"
            title = "中证1000"        
        return (cur_code, title)

    def _option_delta_all(self, code, date_range):
        start_date = datetime.strptime(date_range[0], "%Y%m%d")
        end_date = datetime.strptime(date_range[1], "%Y%m%d")
        current_date = start_date
        current_month = start_date.replace(day=1)
        delta_data = []  # List to store delta information

        while current_date < end_date:
            if(current_date.day == 1):
                current_month = current_date.replace(day=1)
                (near_month, far_month) = self._month_sel(current_month)
                near_year = str(near_month.year)[-2:]
                far_year = str(far_month.year)[-2:]
                near_month_contract = self._load_data(f"{code}{near_year}{near_month.month:02d}")
                far_month_contract = self._load_data(f"{code}{far_year}{far_month.month:02d}")
            try:
                # print(f"Date: {current_date.strftime('%Y-%m-%d')}, near_month: {near_month}, far_month: {far_month}")
                if current_date.strftime("%Y-%m-%d") in near_month_contract['date'].values and \
                   current_date.strftime("%Y-%m-%d") in far_month_contract['date'].values:
                    near_close = near_month_contract[near_month_contract['date'] == current_date.strftime("%Y-%m-%d")].iloc[0]['close']
                    far_close = far_month_contract[far_month_contract['date'] == current_date.strftime("%Y-%m-%d")].iloc[0]['close']
                    delta = far_close - near_close
                    delta_data.append({
                        'date': current_date.strftime('%Y-%m-%d'),
                        'near_close': near_close,
                        'far_close': far_close,
                        'delta': delta
                    })
                current_date += timedelta(days=1)
            except Exception as e:
                raise ValueError(f"Error processing date {current_date.strftime('%Y-%m-%d')}") from e

        
        return pd.DataFrame(delta_data)  # Convert list to DataFrame and return
