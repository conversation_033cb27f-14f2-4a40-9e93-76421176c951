import matplotlib.pyplot as plt
from datetime import datetime, timedelta

class industry_congestion_analyze:
    def __init__(self):
        self.market_data = None

    def _calculate_amount_ratio(self, date_str, daily_market_data, daily_industry_data, industry):
        """计算行业成交量占比"""
        if not daily_market_data.empty:
            total_amount = daily_market_data['amount'].sum()
            if not daily_industry_data.empty:
                industry_amount = daily_industry_data['amount'].sum()
                ratio = industry_amount / total_amount
                self.amount_ratio[industry].append((date_str, ratio))
            else:
                self.amount_ratio[industry].append((date_str, 0))
        else:
            self.amount_ratio[industry].append((date_str, 0))

    def _congestion_analysis(self):
        self.amount_ratio = {industry: [] for industry in self.industries}
        self.pct_change = {industry: [] for industry in self.industries}

        start_date = datetime.strptime(self.date_range[0], "%Y%m%d")
        end_date = datetime.strptime(self.date_range[1], "%Y%m%d")
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime("%Y-%m-%d")
            daily_market_data = self.market_data[self.market_data['date'] == date_str]
            for industry in self.industries:
                daily_industry_data = self.industry_data[industry][self.industry_data[industry]['date'] == date_str]
                self._calculate_amount_ratio(date_str, daily_market_data, daily_industry_data, industry)
                self._calculate_pct_change(date_str, daily_industry_data, industry)
            current_date += timedelta(days=1)

    def _output_analysis(self):
        """将成交量占比和涨跌幅结果合并到一个表格文件中, 写到data/output文件夹中"""
        output_data = []
        for date in [x[0] for x in self.amount_ratio[self.industries[0]]]:
            row = [date]
            for industry in self.industries:
                amount_ratio = next((x[1] for x in self.amount_ratio[industry] if x[0] == date), 0)
                pct_change = next((x[1] for x in self.pct_change[industry] if x[0] == date), 0)
                row.extend([f'{amount_ratio:.2%}', f'{pct_change:.2%}'])
            output_data.append(row)

        columns = ['日期']
        for industry in self.industries:
            columns.append(f'{industry}交易占比')
            columns.append(f'{industry}涨跌幅')

        output_df = pd.DataFrame(output_data, columns=columns)
        os.makedirs('data/output', exist_ok=True)
        output_df.to_csv("data/output/industry_analysis.csv", index=False, encoding='utf_8_sig')

    def plot_industry_ratio(self):
        """计算每日行业交易额占总交易额的比重，并画成折线图"""
        self._load_data()
        self._industry_analysis()
        self._output_analysis()

        dates = [x[0] for x in self.amount_ratio[self.industries[0]]]

        os.makedirs('data/output', exist_ok=True)

        for industry in self.industries:
            amount_ratio = [x[1] for x in self.amount_ratio[industry]]
            pct_change = [x[1] for x in self.pct_change[industry]]

            plt.figure(figsize=(14, 8))
            plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置字体为黑体
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

            plt.plot(dates, amount_ratio, label=f'{industry}交易占比')
            plt.plot(dates, pct_change, label=f'{industry}涨跌幅')

            plt.xlabel('日期')
            plt.ylabel('百分比')
            plt.title(f'{industry}每日交易额占比和涨跌幅')
            plt.legend()
            plt.xticks(rotation=45)
            plt.gca().xaxis.set_major_locator(plt.MaxNLocator(nbins=10))  # 每周一显示一次日期
            plt.tight_layout()
            plt.savefig(f'data/output/{industry}_ratio_pct_change.png')
            plt.close()        