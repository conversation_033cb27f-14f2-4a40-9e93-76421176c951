# 单因子研究框架 API 设计文档

## 1. 核心类设计

### 1.1 FactorResearch - 主入口类

```python
class FactorResearch:
    """
    单因子研究框架的主入口类，提供完整的因子分析工作流
    """
    
    def __init__(self, config_path: str = None, db_manage=None):
        """
        初始化因子研究框架
        
        Parameters:
        -----------
        config_path : str, optional
            配置文件路径，默认使用内置配置
        db_manage : DBManage, optional
            数据库管理对象，用于与现有系统集成
        """
        
    def calculate_factor(self, factor_name: str, assets: List[str], 
                        start_date: str, end_date: str, **kwargs) -> pd.Series:
        """
        计算指定因子
        
        Parameters:
        -----------
        factor_name : str
            因子名称，如 'momentum_20d', 'rsi_14', 'pe_ratio'
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期，格式 'YYYY-MM-DD'
        end_date : str
            结束日期，格式 'YYYY-MM-DD'
        **kwargs : dict
            因子计算参数
            
        Returns:
        --------
        pd.Series
            MultiIndex (date, asset) 的因子值序列
        """
        
    def analyze_factor(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20],
                      quantiles: int = 5, **kwargs) -> Dict:
        """
        分析因子有效性
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据，MultiIndex (date, asset)
        periods : List[int]
            持有期列表，单位为交易日
        quantiles : int
            分位数数量
        **kwargs : dict
            alphalens分析参数
            
        Returns:
        --------
        Dict
            包含IC分析、分层回测等结果的字典
        """
        
    def batch_analyze_factors(self, factor_names: List[str], assets: List[str],
                             start_date: str, end_date: str, **kwargs) -> Dict:
        """
        批量分析多个因子
        
        Parameters:
        -----------
        factor_names : List[str]
            因子名称列表
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns:
        --------
        Dict
            {factor_name: analysis_results} 的字典
        """
        
    def generate_report(self, analysis_results: Dict, output_path: str = None) -> str:
        """
        生成因子分析报告
        
        Parameters:
        -----------
        analysis_results : Dict
            因子分析结果
        output_path : str, optional
            报告输出路径，默认生成HTML格式
            
        Returns:
        --------
        str
            报告内容或文件路径
        """
        
    def plot_factor_analysis(self, analysis_results: Dict, save_path: str = None):
        """
        绘制因子分析图表
        
        Parameters:
        -----------
        analysis_results : Dict
            因子分析结果
        save_path : str, optional
            图表保存路径
        """
```

### 1.2 DataAdapter - 数据适配器

```python
class DataAdapter:
    """
    数据适配器，负责与现有数据库系统集成
    """
    
    def __init__(self, db_manage):
        """
        初始化数据适配器
        
        Parameters:
        -----------
        db_manage : DBManage
            数据库管理对象
        """
        
    def get_price_data(self, assets: List[str], start_date: str, end_date: str,
                      freq: str = "daily") -> pd.DataFrame:
        """
        获取价格数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        freq : str
            数据频率，'daily', 'weekly', 'monthly'
            
        Returns:
        --------
        pd.DataFrame
            MultiIndex (date, asset) 的价格数据
            columns: ['open', 'high', 'low', 'close', 'volume', 'amount']
        """
        
    def get_fundamental_data(self, assets: List[str], start_date: str, end_date: str,
                           fields: List[str] = None) -> pd.DataFrame:
        """
        获取基本面数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        fields : List[str], optional
            需要的字段列表，如 ['pe', 'pb', 'roe', 'revenue']
            
        Returns:
        --------
        pd.DataFrame
            MultiIndex (date, asset) 的基本面数据
        """
        
    def get_industry_classification(self, assets: List[str]) -> pd.Series:
        """
        获取行业分类数据
        
        Parameters:
        -----------
        assets : List[str]
            股票代码列表
            
        Returns:
        --------
        pd.Series
            Index为股票代码，值为行业分类的序列
        """
        
    def format_for_alphalens(self, factor_data: pd.DataFrame, 
                           price_data: pd.DataFrame = None,
                           groupby_data: pd.Series = None) -> Tuple:
        """
        将数据格式化为alphalens所需格式
        
        Parameters:
        -----------
        factor_data : pd.DataFrame
            因子数据
        price_data : pd.DataFrame, optional
            价格数据，如果为None则自动获取
        groupby_data : pd.Series, optional
            分组数据，如行业分类
            
        Returns:
        --------
        Tuple
            (factor_data, pricing_data, groupby_data) 元组
        """
```

### 1.3 FactorEngine - 因子计算引擎

```python
class FactorEngine:
    """
    因子计算引擎，负责各种因子的计算
    """
    
    def __init__(self, data_adapter: DataAdapter):
        """
        初始化因子计算引擎
        
        Parameters:
        -----------
        data_adapter : DataAdapter
            数据适配器对象
        """
        
    def register_factor(self, factor_class: Type[BaseFactor]) -> None:
        """
        注册新的因子类
        
        Parameters:
        -----------
        factor_class : Type[BaseFactor]
            继承自BaseFactor的因子类
        """
        
    def get_available_factors(self) -> List[str]:
        """
        获取可用的因子列表
        
        Returns:
        --------
        List[str]
            可用因子名称列表
        """
        
    def calculate_factor(self, factor_name: str, assets: List[str],
                        start_date: str, end_date: str, **params) -> pd.Series:
        """
        计算指定因子
        
        Parameters:
        -----------
        factor_name : str
            因子名称
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        **params : dict
            因子计算参数
            
        Returns:
        --------
        pd.Series
            MultiIndex (date, asset) 的因子值
        """
        
    def calculate_multiple_factors(self, factor_names: List[str], 
                                 assets: List[str], start_date: str, 
                                 end_date: str, **params) -> Dict[str, pd.Series]:
        """
        批量计算多个因子
        
        Parameters:
        -----------
        factor_names : List[str]
            因子名称列表
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        **params : dict
            因子计算参数
            
        Returns:
        --------
        Dict[str, pd.Series]
            {factor_name: factor_values} 的字典
        """
```

### 1.4 FactorAnalyzer - 因子分析器

```python
class FactorAnalyzer:
    """
    因子分析器，基于alphalens进行因子有效性分析
    """
    
    def __init__(self, data_adapter: DataAdapter):
        """
        初始化因子分析器
        
        Parameters:
        -----------
        data_adapter : DataAdapter
            数据适配器对象
        """
        
    def analyze_factor(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20],
                      quantiles: int = 5, groupby: pd.Series = None, **kwargs) -> Dict:
        """
        完整的因子分析
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据，MultiIndex (date, asset)
        periods : List[int]
            持有期列表
        quantiles : int
            分位数数量
        groupby : pd.Series, optional
            分组数据，如行业分类
        **kwargs : dict
            alphalens参数
            
        Returns:
        --------
        Dict
            包含所有分析结果的字典
        """
        
    def ic_analysis(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20]) -> pd.DataFrame:
        """
        IC分析
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据
        periods : List[int]
            持有期列表
            
        Returns:
        --------
        pd.DataFrame
            IC分析结果
        """
        
    def quantile_analysis(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20],
                         quantiles: int = 5) -> Dict:
        """
        分位数分析（分层回测）
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据
        periods : List[int]
            持有期列表
        quantiles : int
            分位数数量
            
        Returns:
        --------
        Dict
            分位数分析结果
        """
        
    def turnover_analysis(self, factor_data: pd.Series, periods: List[int] = [1, 5, 10, 20],
                         quantiles: int = 5) -> pd.DataFrame:
        """
        换手率分析
        
        Parameters:
        -----------
        factor_data : pd.Series
            因子数据
        periods : List[int]
            持有期列表
        quantiles : int
            分位数数量
            
        Returns:
        --------
        pd.DataFrame
            换手率分析结果
        """
```

## 2. 因子基类设计

### 2.1 BaseFactor - 因子基类

```python
from abc import ABC, abstractmethod

class BaseFactor(ABC):
    """
    因子基类，所有因子都应继承此类
    """
    
    def __init__(self, name: str, description: str = ""):
        """
        初始化因子
        
        Parameters:
        -----------
        name : str
            因子名称
        description : str
            因子描述
        """
        self.name = name
        self.description = description
        
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算因子值
        
        Parameters:
        -----------
        data : pd.DataFrame
            输入数据，MultiIndex (date, asset)
        **params : dict
            计算参数
            
        Returns:
        --------
        pd.Series
            因子值，MultiIndex (date, asset)
        """
        pass
        
    @abstractmethod
    def get_required_data(self) -> List[str]:
        """
        获取计算因子所需的数据字段
        
        Returns:
        --------
        List[str]
            所需数据字段列表，如 ['close', 'volume', 'pe']
        """
        pass
        
    def validate_params(self, **params) -> bool:
        """
        验证计算参数
        
        Parameters:
        -----------
        **params : dict
            计算参数
            
        Returns:
        --------
        bool
            参数是否有效
        """
        return True
        
    def get_default_params(self) -> Dict:
        """
        获取默认参数
        
        Returns:
        --------
        Dict
            默认参数字典
        """
        return {}
```

## 3. 配置管理

### 3.1 FactorConfig - 配置管理类

```python
class FactorConfig:
    """
    因子研究框架配置管理
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化配置
        
        Parameters:
        -----------
        config_path : str, optional
            配置文件路径
        """
        
    def get_data_config(self) -> Dict:
        """获取数据源配置"""
        
    def get_factor_config(self) -> Dict:
        """获取因子计算配置"""
        
    def get_analysis_config(self) -> Dict:
        """获取分析配置"""
        
    def get_visualization_config(self) -> Dict:
        """获取可视化配置"""
```

这个API设计提供了清晰的接口定义，便于实现和使用。每个类都有明确的职责分工，同时保持了良好的扩展性。
