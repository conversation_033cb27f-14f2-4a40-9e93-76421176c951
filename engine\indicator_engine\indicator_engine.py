import talib
import numpy as np
import pandas as pd

class IndicatorEngine:
    """
    公共信号逻辑基类，供回测策略和交易策略继承或调用。
    """

    def __init__(self, ma_period=20, boll_period=20, boll_dev=2):
        self.ma_period = ma_period
        self.boll_period = boll_period
        self.boll_dev = boll_dev

    def calc_ma(self, close, period=None):
        """
        计算移动平均线
        """
        period = period or self.ma_period
        return talib.SMA(np.asarray(close, dtype=float), timeperiod=period)

    def calc_boll(self, close, period=None, dev=None):
        """
        计算布林带
        说明：必须传入长度 >= period 的close序列，talib.BBANDS 才能返回有效的布林带数值。
        如果数据长度不足，返回的upper/middle/lower会是np.nan。
        """
        period = period or self.boll_period
        dev = dev or self.boll_dev
        upper, middle, lower = talib.BBANDS(np.asarray(close, dtype=float), timeperiod=period, nbdevup=dev, nbdevdn=dev)
        return upper, middle, lower

    def calc_rsi(self, close, period=14):
        """
        计算RSI
        """
        return talib.RSI(np.asarray(close, dtype=float), timeperiod=period)


    def _calculate_single_indicator(self, ind_name, ind_param, data, close):
        """
        根据指标名和参数动态计算单个指标，返回结果dict
        """
        results = {}
        if ind_name == "bollinger":
            period = ind_param.get("period", self.boll_period)
            dev = ind_param.get("dev", self.boll_dev)
            upper, middle, lower = self.calc_boll(close, period, dev)
            results[f"boll_upper_{period}_{dev}"] = upper[-1] if len(upper) > 0 else None
            results[f"boll_middle_{period}_{dev}"] = middle[-1] if len(middle) > 0 else None
            results[f"boll_lower_{period}_{dev}"] = lower[-1] if len(lower) > 0 else None
        elif ind_name == "vol_ma":
            period = ind_param.get("period", self.ma_period)
            # 计算成交量均线
            if isinstance(data, dict):
                volume = [data.get("volume", 0)]
            else:
                volume = data["volume"].astype(float).values
            ma = talib.SMA(np.asarray(volume, dtype=float), timeperiod=period)
            results[f"vol_ma_{period}"] = ma[-1] if len(ma) > 0 else None
        elif ind_name == "ma":
            period = ind_param.get("period", self.ma_period) if isinstance(ind_param, dict) else ind_param
            ma = self.calc_ma(close, period)
            results[f"ma{period}"] = ma[-1] if len(ma) > 0 else None
        elif ind_name == "rsi":
            period = ind_param.get("period", 14) if isinstance(ind_param, dict) else ind_param
            rsi = self.calc_rsi(close, period)
            results[f"rsi{period}"] = rsi[-1] if len(rsi) > 0 else None
        # 可扩展更多指标
        return results

    def calculate(self, data, params=None):
        """
        计算所有需要的指标，返回结果dict。
        :param data: 当前bar或DataFrame
        :param params: 指标参数dict（如有需要可传入），如{"bollinger": {"period": 30, "dev": 3}, "vol_ma": {"period": 10}}
        """
        results = {}
        close = None
        if isinstance(data, dict):
            close = [data.get("close", 0)]
        elif isinstance(data, pd.DataFrame):
            close = data["close"].astype(float).values
        if close is None:
            return results

        if params is None:
            params = {}

        # 动态调用每个指标的计算方法
        for ind_name, ind_param in params.items():
            single_result = self._calculate_single_indicator(ind_name, ind_param, data, close)
            results.update(single_result)
        return results

    def add_indicators(self, df: pd.DataFrame, indicators: dict = None):
        """
        在DataFrame上批量添加常用指标
        indicators: {"ma": 20, "boll": (20,2), "rsi": 14}
        支持策略自定义指标参数，调用时请优先传入策略自定义的 indicators 字典。
        """
        if indicators is None:
            indicators = {}
        close = df["close"].astype(float).values
        if "ma" in indicators:
            period = indicators["ma"]
            df[f"ma{period}"] = self.calc_ma(close, period)
        if "boll" in indicators:
            period, dev = indicators["boll"]
            upper, middle, lower = self.calc_boll(close, period, dev)
            df[f"boll_upper_{period}"] = upper
            df[f"boll_middle_{period}"] = middle
            df[f"boll_lower_{period}"] = lower
        if "rsi" in indicators:
            period = indicators["rsi"]
            df[f"rsi{period}"] = self.calc_rsi(close, period)
        return df

    @staticmethod
    def _config_key(indicator_cls, params: dict):
        """
        生成唯一key，indicator类型+参数
        """
        items = tuple(sorted((params or {}).items()))
        return (indicator_cls.__name__, items)

    @classmethod
    def assemble_indicator_map(cls, equity_pool, default_indicator_cls=None, default_params=None):
        """
        统计所有标的用到的指标类型和参数，去重实例化，并为每个标的分配实例。
        :param equity_pool: 股票池list，每项为dict，需包含code和可选indicator
        :param default_indicator_cls: 默认指标类
        :param default_params: 默认参数
        :return: symbol_indicator_map
        """
        indicator_instance_map = {}
        symbol_indicator_map = {}
        for item in equity_pool:
            symbol = item["code"] if isinstance(item, dict) else str(item)
            indicator_cfg = item.get("indicator", {}) if isinstance(item, dict) else {}
            indicator_cls = indicator_cfg.get("class", default_indicator_cls or cls)
            indicator_params = indicator_cfg.get("params", default_params or {})
            key = cls._config_key(indicator_cls, indicator_params)
            if key not in indicator_instance_map:
                indicator_instance_map[key] = indicator_cls(**(indicator_params or {}))
            symbol_indicator_map[symbol] = indicator_instance_map[key]
        return symbol_indicator_map

