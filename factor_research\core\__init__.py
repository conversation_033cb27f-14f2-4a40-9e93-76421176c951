"""
核心模块

包含因子研究框架的核心组件:
- DataAdapter: 数据适配器
- FactorEngine: 因子计算引擎  
- FactorAnalyzer: 因子分析器
- FactorVisualizer: 结果可视化
- FactorResearch: 主入口类
"""

from .data_adapter import DataAdapter
from .factor_engine import FactorEngine
from .analyzer import FactorAnalyzer
from .visualizer import FactorVisualizer
from .factor_research import FactorResearch

__all__ = [
    'DataAdapter',
    'FactorEngine',
    'FactorAnalyzer',
    'FactorVisualizer',
    'FactorResearch'
]
from .factor_research import FactorResearch

__all__ = [
    "DataAdapter",
    "FactorEngine", 
    "FactorAnalyzer",
    "FactorVisualizer",
    "FactorResearch"
]
