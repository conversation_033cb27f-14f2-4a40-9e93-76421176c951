from .base import BaseProportionSizer

class TradeSizer(BaseProportionSizer):
    def __init__(self, *args, position_manager=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.position_manager = position_manager  # 可选，便于访问风控或持仓信息

    def get_buy_size(self, cash, price, max_position_value=None):
        return self.calc_buy_size(cash, price, max_position_value=max_position_value)

    def get_sell_size(self, position_size):
        return self.calc_sell_size(position_size)

    def get_size(self, symbol, signal, position, broker):
        if signal > 0:
            # 买入信号
            cash = broker.get_cash()
            price = broker.get_price(symbol)
            max_position_value = self.position_manager.get_max_position_value(symbol)
            size = self.get_buy_size(cash, price, max_position_value=max_position_value)
        elif signal < 0:
            # 卖出信号
            position_size = position['quantity'] if position else 0
            size = self.get_sell_size(position_size)
        else:
            # 无信号或平仓信号
            size = 0

        return size