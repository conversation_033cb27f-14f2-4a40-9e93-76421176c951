import importlib
import json

class StrategyManager:
    """
    策略工厂与map组装管理器
    """

    @staticmethod
    def create_strategy_instance(strategy_cls, name, sizer, indicator, position_manager, broker, params=None):
        """
        统一创建策略实例
        """
        return strategy_cls(name, sizer, indicator, position_manager, broker, params)

    @staticmethod
    def get_strategy_class(strategy_name, mode):
        """
        根据策略名和mode返回对应的策略类
        """
        print(f"加载策略: {strategy_name}，模式: {mode}")
        if mode == 'trade':
            module_path = f"strategies.trade_strategy.{strategy_name}.trade_strategy"
            class_name = f"Trade{''.join([part.capitalize() for part in strategy_name.split('_')])}"
        elif mode == 'bt':
            module_path = f"strategies.trade_strategy.{strategy_name}.bt_strategy"
            class_name = f"Bt{''.join([part.capitalize() for part in strategy_name.split('_')])}"
        else:
            raise ValueError(f"不支持的mode: {mode}")
        
        print(f"导入模块: {module_path}, 类名: {class_name}")

        module = importlib.import_module(module_path)
        return getattr(module, class_name)

    @staticmethod
    def assemble_strategy_map(equity_pool, mode='trade', default_strategy_name=None, default_params=None,
                             sizer_map=None, indicator_map=None, position_manager=None, broker=None):
        """
        根据股票池配置为每个标的组装唯一的策略实例，支持参数去重。
        position_manager为全局实例，所有标的共用。
        :param equity_pool: 股票池list，每项为dict，需包含code和可选strategy
        :param mode: 'trade' 或 'bt'
        :param default_strategy_name: 默认策略名
        :param default_params: 默认参数
        :param sizer_map: symbol->sizer实例
        :param indicator_map: symbol->indicator实例
        :param position_manager: 全局position_manager实例
        :param broker: broker实例
        :return: symbol_strategy_map
        """
        strategy_instance_map = {}
        symbol_strategy_map = {}
        for item in equity_pool:
            symbol = item["code"] if isinstance(item, dict) else str(item)
            strategy_cfg = item.get("strategy", {}) if isinstance(item, dict) else {}
            strategy_name = strategy_cfg.get("name", default_strategy_name)
            strategy_params = strategy_cfg.get("params", default_params or {})
            print(f"处理标的: {symbol}, 策略: {strategy_name}, 参数: {strategy_params}")
            params_key = json.dumps(strategy_params, sort_keys=True, ensure_ascii=False)
            print(f"参数唯一键: {params_key}")
            key = (strategy_name, params_key)
            # --- end 修改 ---
            if key not in strategy_instance_map:
                strategy_cls = StrategyManager.get_strategy_class(strategy_name, mode)
                sizer = sizer_map[symbol] if sizer_map else None
                indicator = indicator_map[symbol] if indicator_map else None
                strategy_instance_map[key] = StrategyManager.create_strategy_instance(
                    strategy_cls,
                    name=strategy_name,
                    sizer=sizer,
                    indicator=indicator,
                    position_manager=position_manager,
                    broker=broker,
                    params=strategy_params
                )
            symbol_strategy_map[symbol] = strategy_instance_map[key]
        return symbol_strategy_map
