import os
import pandas as pd
from analyze.industry_analyze.industry_macd_amount_sel import IndustryMACDAmountSelector
from datetime import datetime, timedelta
from data_gen.data_generator_em import DataGeneratorEM

class IndustryAnalyzer:
    """行业分析核心类"""
    def __init__(self, generator: DataGeneratorEM):
        self.generator = generator
        self.industry_data = {}
        self.industries = generator.industries
        self.selector = None

    def _load_data(self, period):
        """加载数据"""
        # self.market_data = self.generator._read_csv("data/amarket.csv")
        for industry in self.industries:
            self.industry_data[industry] = self.generator._read_csv(f"data/industries/{period}/{industry}.csv")

    def _analyze(self, selector, optimize, analyze_period):
        """行业筛选"""
        if(analyze_period != "week" and analyze_period != "month" and analyze_period != "day"):
            self.generator._merge_history_data(analyze_period)
        self._load_data(analyze_period)
        selector = selector(self.industries, self.industry_data, analyze_period)
        if(optimize):
            selector._parm_optimize(analyze_period)
        selector.cal_signal()

    def _industry_predict(self, optimize, analyze_period):
        """行业筛选"""
        if(analyze_period != "week" and analyze_period != "month" and analyze_period != "day"):
            self.generator._merge_history_data(analyze_period)
        self._load_data(analyze_period)
        imacd_selector = IndustryMACDAmountSelector(self.industries, self.industry_data, analyze_period)
        if(optimize):
            imacd_selector._parm_optimize(analyze_period)

        imacd_selector._output_predict()

    def read_signal(self, date):
        positive_list = []
        for industry in self.industries:
            predict_path = f"data/output/industry_predict/{industry}.csv"
            if os.path.exists(predict_path):
                df = pd.read_csv(predict_path, encoding='utf_8_sig')
                # Find the row corresponding to the given date
                row = df[df['date'] == date]
                if not row.empty:
                    positive_signal = row['positive_signal'].values[0]
                    if(positive_signal):
                        positive_list.append(industry)
                else:
                    print(f"Industry: {industry}, Date: {date} not found in data")
        
        return positive_list