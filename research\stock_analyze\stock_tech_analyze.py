class StockTechAnalyze:
    def __init__(self, df):
        self.sell_weight = {}
        self.buy_weith = {}

    def _default_weight(self):
        """默认权重"""
        self.sell_weight = {
            'macd': 0.2,
            'ma': 0.2,
            'amount': 0.2,            
        }
        self.buy_weith = {
            'macd': 0.2,
            'ma': 0.2,
            'amount': 0.2
        }

    def _stock_amount_analyze(self):
        pass

    def _stock_ma_analyze(self):
        pass

    def _stock_macd_analyze(self):
        pass

    def _signal_cal(self, point):
        """Calculate the signal based on the given point"""
        pass

    def _stock_buy_signal(self):
        pass

    def _stock_sell_signal(self):
        pass