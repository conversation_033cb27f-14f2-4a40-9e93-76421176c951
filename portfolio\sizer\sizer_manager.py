import importlib

class SizerManager:
    """
    负责统计唯一sizer配置、生成sizer实例，并为每个标的分配实例。
    支持trade/bt等多种模式。
    """
    def __init__(self, mode='trade', position_manager=None):
        self.mode = mode
        self.position_manager = position_manager  # 可选，便于访问风控或持仓信息
        self.sizer_instance_map = {}  # {config_key: sizer_instance}
        self.symbol_sizer_map = {}    # {symbol: sizer_instance}

    def create_sizer(self, sizer_name='proportion_sizer', mode='trade', **kwargs):
        """
        通用sizer工厂（实例方法，便于访问self.position_manager）
        """
        module_path = f".{sizer_name}"
        if mode == 'trade':
            sizer_module = importlib.import_module(f"{module_path}.trade_sizer", __package__)
            TradeSizer = getattr(sizer_module, "TradeSizer")
            # 将position_manager传递给TradeSizer
            return TradeSizer(position_manager=self.position_manager, **kwargs)

        elif mode == 'bt':
            bt_sizer_module = importlib.import_module(f"{module_path}.bt_sizer", __package__)
            BtSizer = getattr(bt_sizer_module, "BtSizer")
            class CustomSizer(BtSizer):
                params = tuple((k, v) for k, v in kwargs.items())
            return CustomSizer
        else:
            raise ValueError(f"不支持的mode: {mode}")

    @staticmethod
    def _config_key(sizer_name, sizer_params):
        # 生成唯一key，可根据实际需要调整
        items = tuple(sorted((sizer_params or {}).items()))
        return (sizer_name, items)

    def assemble(self, equity_pool, default_sizer_name='proportion_sizer', default_sizer_params=None):
        """
        统计股票池中所有唯一sizer配置，为每种配置生成实例，并为每个标的分配。
        :param equity_pool: 股票池list，每项为dict，需包含code和可选sizer
        :param default_sizer_name: 默认sizer类型
        :param default_sizer_params: 默认sizer参数
        :return: symbol_sizer_map
        """
        self.sizer_instance_map.clear()
        self.symbol_sizer_map.clear()
        for item in equity_pool:
            symbol = item["code"] if isinstance(item, dict) else str(item)
            sizer_cfg = item.get("sizer", {}) if isinstance(item, dict) else {}
            sizer_name = sizer_cfg.get("name", default_sizer_name)
            sizer_params = sizer_cfg.get("params", default_sizer_params or {})
            key = self._config_key(sizer_name, sizer_params)
            if key not in self.sizer_instance_map:
                self.sizer_instance_map[key] = self.create_sizer(
                    sizer_name=sizer_name, mode=self.mode, **(sizer_params or {})
                )
            self.symbol_sizer_map[symbol] = self.sizer_instance_map[key]
        return self.symbol_sizer_map