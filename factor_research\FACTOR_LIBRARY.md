# 因子库设计文档

## 1. 因子分类体系

### 1.1 技术因子 (Technical Factors)

#### 1.1.1 动量因子 (Momentum Factors)
- **momentum_Nd**: N日动量因子，计算过去N日的收益率
- **rsi_N**: N日相对强弱指数
- **williams_r_N**: N日威廉指标
- **stoch_k_N**: N日随机指标K值
- **stoch_d_N**: N日随机指标D值

#### 1.1.2 反转因子 (Reversal Factors)
- **short_reversal_N**: N日短期反转因子
- **overnight_return**: 隔夜收益率
- **intraday_return**: 日内收益率

#### 1.1.3 波动率因子 (Volatility Factors)
- **volatility_N**: N日收益率标准差
- **realized_vol_N**: N日已实现波动率
- **garman_klass_vol**: Garman-Klass波动率估计
- **parkinson_vol**: Parkinson波动率估计

#### 1.1.4 成交量因子 (Volume Factors)
- **volume_ratio_N**: N日成交量比率
- **turnover_rate_N**: N日换手率
- **vwap_ratio**: 成交量加权平均价格比率
- **price_volume_trend**: 价量趋势指标

#### 1.1.5 价格因子 (Price Factors)
- **price_relative_N**: N日相对价格位置
- **high_low_ratio**: 最高最低价比率
- **close_to_high**: 收盘价相对最高价位置
- **close_to_low**: 收盘价相对最低价位置

### 1.2 基本面因子 (Fundamental Factors)

#### 1.2.1 估值因子 (Valuation Factors)
- **pe_ratio**: 市盈率
- **pb_ratio**: 市净率
- **ps_ratio**: 市销率
- **pcf_ratio**: 市现率
- **ev_ebitda**: 企业价值倍数

#### 1.2.2 盈利能力因子 (Profitability Factors)
- **roe**: 净资产收益率
- **roa**: 总资产收益率
- **gross_margin**: 毛利率
- **operating_margin**: 营业利润率
- **net_margin**: 净利润率

#### 1.2.3 成长因子 (Growth Factors)
- **revenue_growth**: 营收增长率
- **profit_growth**: 利润增长率
- **eps_growth**: 每股收益增长率
- **book_value_growth**: 每股净资产增长率

#### 1.2.4 财务质量因子 (Quality Factors)
- **debt_to_equity**: 资产负债率
- **current_ratio**: 流动比率
- **quick_ratio**: 速动比率
- **interest_coverage**: 利息保障倍数

### 1.3 宏观因子 (Macro Factors)

#### 1.3.1 市场因子 (Market Factors)
- **market_beta**: 市场贝塔
- **market_cap**: 市值因子
- **free_float_cap**: 流通市值因子

#### 1.3.2 行业因子 (Industry Factors)
- **industry_momentum**: 行业动量
- **industry_reversal**: 行业反转
- **industry_relative_strength**: 行业相对强度

## 2. 因子实现规范

### 2.1 技术因子实现示例

```python
class MomentumFactor(BaseFactor):
    """
    动量因子：计算过去N日的收益率
    """
    
    def __init__(self, window: int = 20):
        super().__init__(
            name=f"momentum_{window}d",
            description=f"{window}日动量因子"
        )
        self.window = window
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算动量因子
        
        Parameters:
        -----------
        data : pd.DataFrame
            包含close价格的数据，MultiIndex (date, asset)
        
        Returns:
        --------
        pd.Series
            动量因子值
        """
        window = params.get('window', self.window)
        
        # 计算收益率
        returns = data['close'].groupby(level=1).pct_change(window)
        
        return returns
        
    def get_required_data(self) -> List[str]:
        return ['close']
        
    def get_default_params(self) -> Dict:
        return {'window': self.window}

class RSIFactor(BaseFactor):
    """
    RSI因子：相对强弱指数
    """
    
    def __init__(self, window: int = 14):
        super().__init__(
            name=f"rsi_{window}",
            description=f"{window}日RSI指标"
        )
        self.window = window
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算RSI因子
        """
        window = params.get('window', self.window)
        
        def calculate_rsi(prices):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
            
        rsi_values = data['close'].groupby(level=1).apply(calculate_rsi)
        
        return rsi_values
        
    def get_required_data(self) -> List[str]:
        return ['close']

class VolatilityFactor(BaseFactor):
    """
    波动率因子：计算收益率标准差
    """
    
    def __init__(self, window: int = 20):
        super().__init__(
            name=f"volatility_{window}d",
            description=f"{window}日波动率"
        )
        self.window = window
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算波动率因子
        """
        window = params.get('window', self.window)
        
        # 计算日收益率
        returns = data['close'].groupby(level=1).pct_change()
        
        # 计算滚动标准差
        volatility = returns.groupby(level=1).rolling(window=window).std()
        
        # 重置索引以匹配原始数据格式
        volatility.index = volatility.index.droplevel(0)
        
        return volatility
        
    def get_required_data(self) -> List[str]:
        return ['close']
```

### 2.2 基本面因子实现示例

```python
class PERatioFactor(BaseFactor):
    """
    市盈率因子
    """
    
    def __init__(self):
        super().__init__(
            name="pe_ratio",
            description="市盈率因子"
        )
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算市盈率因子
        
        Parameters:
        -----------
        data : pd.DataFrame
            包含market_cap和net_profit的数据
        """
        # 市盈率 = 市值 / 净利润
        pe_ratio = data['market_cap'] / data['net_profit']
        
        # 处理异常值
        pe_ratio = pe_ratio.replace([np.inf, -np.inf], np.nan)
        
        return pe_ratio
        
    def get_required_data(self) -> List[str]:
        return ['market_cap', 'net_profit']

class ROEFactor(BaseFactor):
    """
    净资产收益率因子
    """
    
    def __init__(self):
        super().__init__(
            name="roe",
            description="净资产收益率"
        )
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算ROE因子
        """
        # ROE = 净利润 / 净资产
        roe = data['net_profit'] / data['total_equity']
        
        return roe
        
    def get_required_data(self) -> List[str]:
        return ['net_profit', 'total_equity']

class RevenueGrowthFactor(BaseFactor):
    """
    营收增长率因子
    """
    
    def __init__(self, periods: int = 4):
        super().__init__(
            name=f"revenue_growth_{periods}q",
            description=f"{periods}季度营收增长率"
        )
        self.periods = periods
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算营收增长率
        """
        periods = params.get('periods', self.periods)
        
        # 计算同比增长率
        revenue_growth = data['revenue'].groupby(level=1).pct_change(periods)
        
        return revenue_growth
        
    def get_required_data(self) -> List[str]:
        return ['revenue']
```

### 2.3 复合因子实现示例

```python
class PriceVolumeCorrelationFactor(BaseFactor):
    """
    价量相关性因子
    """
    
    def __init__(self, window: int = 20):
        super().__init__(
            name=f"price_volume_corr_{window}d",
            description=f"{window}日价量相关性"
        )
        self.window = window
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算价量相关性
        """
        window = params.get('window', self.window)
        
        def rolling_corr(group):
            price_returns = group['close'].pct_change()
            volume_change = group['volume'].pct_change()
            return price_returns.rolling(window=window).corr(volume_change)
            
        correlation = data.groupby(level=1).apply(rolling_corr)
        correlation.index = correlation.index.droplevel(0)
        
        return correlation
        
    def get_required_data(self) -> List[str]:
        return ['close', 'volume']

class QualityScoreFactor(BaseFactor):
    """
    质量评分因子：综合多个财务指标
    """
    
    def __init__(self):
        super().__init__(
            name="quality_score",
            description="财务质量综合评分"
        )
        
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算质量评分
        """
        # 标准化各个指标
        roe_norm = self._normalize(data['roe'])
        debt_ratio_norm = self._normalize(-data['debt_ratio'])  # 负号表示越低越好
        current_ratio_norm = self._normalize(data['current_ratio'])
        
        # 加权平均
        weights = params.get('weights', [0.4, 0.3, 0.3])
        quality_score = (weights[0] * roe_norm + 
                        weights[1] * debt_ratio_norm + 
                        weights[2] * current_ratio_norm)
        
        return quality_score
        
    def _normalize(self, series: pd.Series) -> pd.Series:
        """标准化序列到0-1区间"""
        return (series - series.min()) / (series.max() - series.min())
        
    def get_required_data(self) -> List[str]:
        return ['roe', 'debt_ratio', 'current_ratio']
```

## 3. 因子注册和管理

### 3.1 因子注册机制

```python
class FactorRegistry:
    """
    因子注册表，管理所有可用因子
    """
    
    def __init__(self):
        self._factors = {}
        self._register_default_factors()
        
    def register(self, factor_class: Type[BaseFactor], **default_params):
        """
        注册因子类
        
        Parameters:
        -----------
        factor_class : Type[BaseFactor]
            因子类
        **default_params : dict
            默认参数
        """
        factor_instance = factor_class(**default_params)
        self._factors[factor_instance.name] = {
            'class': factor_class,
            'instance': factor_instance,
            'default_params': default_params
        }
        
    def get_factor(self, name: str) -> BaseFactor:
        """获取因子实例"""
        if name not in self._factors:
            raise ValueError(f"Factor '{name}' not found")
        return self._factors[name]['instance']
        
    def list_factors(self) -> List[str]:
        """列出所有可用因子"""
        return list(self._factors.keys())
        
    def _register_default_factors(self):
        """注册默认因子"""
        # 技术因子
        self.register(MomentumFactor, window=20)
        self.register(RSIFactor, window=14)
        self.register(VolatilityFactor, window=20)
        
        # 基本面因子
        self.register(PERatioFactor)
        self.register(ROEFactor)
        self.register(RevenueGrowthFactor, periods=4)
```

## 4. 因子验证和测试

### 4.1 因子有效性检查

```python
class FactorValidator:
    """
    因子验证器，检查因子计算的正确性
    """
    
    def validate_factor(self, factor: BaseFactor, test_data: pd.DataFrame) -> Dict:
        """
        验证因子计算
        
        Returns:
        --------
        Dict
            验证结果，包括数据完整性、数值范围等
        """
        results = {
            'name': factor.name,
            'valid': True,
            'issues': []
        }
        
        try:
            factor_values = factor.calculate(test_data)
            
            # 检查数据完整性
            if factor_values.isna().all():
                results['valid'] = False
                results['issues'].append("All values are NaN")
                
            # 检查数值范围
            if np.isinf(factor_values).any():
                results['issues'].append("Contains infinite values")
                
            # 检查数据类型
            if not isinstance(factor_values, pd.Series):
                results['valid'] = False
                results['issues'].append("Return type is not pd.Series")
                
        except Exception as e:
            results['valid'] = False
            results['issues'].append(f"Calculation error: {str(e)}")
            
        return results
```

这个因子库设计提供了完整的因子分类体系和实现规范，为后续的因子开发提供了清晰的指导。
