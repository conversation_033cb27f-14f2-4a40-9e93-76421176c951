#!/usr/bin/env python3
"""
调试因子数据索引结构问题
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_factor_calculation():
    """测试因子计算的数据结构"""
    print("=== 调试因子数据索引结构 ===\n")

    try:
        # 导入必要模块
        from factor_research import FactorResearch
        from database.db_manage import DBManage
        from portfolio.account.account_manage import AccountManage

        print("✓ 模块导入成功")

        # 初始化系统
        account_manage = AccountManage()
        db_manage = DBManage(account_manage)

        # 0. 检查数据库表结构
        print("\n0. 检查数据库表结构...")
        try:
            # 查看所有表
            tables_result = db_manage.quant_db.conn.execute("SHOW TABLES").fetchall()
            tables = [row[0] for row in tables_result]
            print(f"  数据库中的表: {tables}")

            # 检查stock_daily表
            if 'stock_daily' in tables:
                count_result = db_manage.quant_db.conn.execute("SELECT COUNT(*) FROM stock_daily").fetchone()
                print(f"  stock_daily表记录数: {count_result[0]}")

                # 查看表结构
                schema_result = db_manage.quant_db.conn.execute("PRAGMA table_info('stock_daily')").fetchall()
                print(f"  stock_daily表结构:")
                for row in schema_result:
                    print(f"    {row[1]} ({row[2]})")

                # 查看样本数据
                sample_result = db_manage.quant_db.conn.execute("SELECT * FROM stock_daily LIMIT 3").fetchall()
                print(f"  stock_daily样本数据:")
                for row in sample_result:
                    print(f"    {row}")
            else:
                print("  ⚠ stock_daily表不存在")

        except Exception as e:
            print(f"  ✗ 数据库检查失败: {e}")

        research = FactorResearch(db_manage=db_manage)
        print("✓ 因子研究框架初始化成功")

        # 测试参数
        assets = ["000001", "000002"]
        start_date = "2023-01-01"
        end_date = "2023-12-31"

        print(f"\n测试参数:")
        print(f"  股票: {assets}")
        print(f"  时间: {start_date} 到 {end_date}")

        # 1. 测试数据获取
        print("\n1. 测试数据获取...")
        try:
            data = research.data_adapter.get_price_data(
                assets=assets,
                start_date=start_date,
                end_date=end_date,
                fields=['close', 'volume']
            )

            print(f"✓ 数据获取成功")
            print(f"  数据形状: {data.shape}")
            print(f"  索引类型: {type(data.index)}")
            print(f"  索引名称: {data.index.names}")

            if isinstance(data.index, pd.MultiIndex):
                print(f"  索引级别数: {data.index.nlevels}")
                print(f"  第一级别样本: {data.index.get_level_values(0)[:5].tolist()}")
                print(f"  第二级别样本: {data.index.get_level_values(1)[:5].tolist()}")
            else:
                print(f"  ⚠ 警告: 索引不是MultiIndex!")
                print(f"  索引内容: {data.index[:10].tolist()}")

            print(f"  列名: {data.columns.tolist()}")
            print(f"  前5行数据:")
            print(data.head())

        except Exception as e:
            print(f"✗ 数据获取失败: {e}")
            return
            
        # 2. 测试因子计算
        print("\n2. 测试因子计算...")
        try:
            factor_data = research.calculate_factor(
                factor_name="MomentumFactor",
                assets=assets,
                start_date=start_date,
                end_date=end_date,
                lookback_period=20,
                skip_period=1
            )
            
            print(f"✓ 因子计算成功")
            print(f"  因子数据类型: {type(factor_data)}")
            print(f"  因子数据形状: {factor_data.shape}")
            print(f"  索引类型: {type(factor_data.index)}")
            print(f"  索引名称: {factor_data.index.names}")
            
            if isinstance(factor_data.index, pd.MultiIndex):
                print(f"  索引级别数: {factor_data.index.nlevels}")
                print(f"  第一级别样本: {factor_data.index.get_level_values(0)[:5].tolist()}")
                print(f"  第二级别样本: {factor_data.index.get_level_values(1)[:5].tolist()}")
            else:
                print(f"  ⚠ 警告: 因子数据索引不是MultiIndex!")
                print(f"  索引内容: {factor_data.index[:10].tolist()}")
                
            print(f"  因子名称: {factor_data.name}")
            print(f"  前5个因子值:")
            print(factor_data.head())
            
        except Exception as e:
            print(f"✗ 因子计算失败: {e}")
            import traceback
            traceback.print_exc()
            return
            
        # 3. 测试因子分析
        print("\n3. 测试因子分析...")
        try:
            # 检查因子数据索引结构
            if not isinstance(factor_data.index, pd.MultiIndex):
                print("✗ 因子数据索引结构错误，无法进行分析")
                return
                
            if factor_data.index.nlevels != 2:
                print(f"✗ 因子数据索引级别错误: {factor_data.index.nlevels}, 期望: 2")
                return
                
            results = research.analyze_factor(factor_data)
            print(f"✓ 因子分析成功")
            print(f"  分析结果类型: {type(results)}")
            print(f"  分析结果键: {list(results.keys()) if isinstance(results, dict) else 'Not a dict'}")
            
        except Exception as e:
            print(f"✗ 因子分析失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def create_test_data():
    """创建测试数据来验证索引结构"""
    print("\n=== 创建测试数据验证索引结构 ===\n")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', '2023-01-10', freq='D')
    assets = ['000001', '000002']
    
    # 方法1: 使用MultiIndex.from_product
    index1 = pd.MultiIndex.from_product([dates, assets], names=['date', 'asset'])
    data1 = pd.DataFrame({
        'close': np.random.randn(len(index1)) + 100,
        'volume': np.random.randint(1000, 10000, len(index1))
    }, index=index1)
    
    print("方法1 - MultiIndex.from_product:")
    print(f"  索引类型: {type(data1.index)}")
    print(f"  索引级别数: {data1.index.nlevels}")
    print(f"  索引名称: {data1.index.names}")
    print(f"  数据形状: {data1.shape}")
    print(f"  前5行:")
    print(data1.head())
    
    # 测试unstack/stack操作
    print("\n测试unstack/stack操作:")
    try:
        unstacked = data1['close'].unstack()
        print(f"  unstack后形状: {unstacked.shape}")
        print(f"  unstack后索引: {unstacked.index.name}")
        print(f"  unstack后列名: {unstacked.columns.name}")
        
        restacked = unstacked.stack()
        print(f"  重新stack后形状: {restacked.shape}")
        print(f"  重新stack后索引类型: {type(restacked.index)}")
        print(f"  重新stack后索引名称: {restacked.index.names}")
        
    except Exception as e:
        print(f"  ✗ unstack/stack操作失败: {e}")

if __name__ == "__main__":
    # 先测试基础数据结构
    create_test_data()
    
    # 再测试实际因子计算
    test_factor_calculation()
