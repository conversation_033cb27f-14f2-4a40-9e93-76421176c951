import backtrader as bt
from strategies.trade_strategy.unified_strategy_base import UnifiedStrategyBase
from strategies.trade_strategy.gold_bollinger_strategy.signal import StrategySignal

class BtGoldBollingerStrategy(bt.Strategy, UnifiedStrategyBase):
    params = (
        ('indicators', {
            'bollinger': {'period': 15, 'dev': 2},
            'vol_ma': {'period': 5}
        }),
    )

    def __init__(self):
        # 适配新参数格式
        indicators = self.p.indicators if hasattr(self.p, "indicators") else {}
        boll_params = indicators.get("bollinger", {})
        vol_ma_params = indicators.get("vol_ma", {})
        UnifiedStrategyBase.__init__(
            self,
            name="bt_gold_bollinger",
            sizer=None,  # 可按需传入
            indicator=None,  # 可按需传入
            position_manager=None,  # 可按需传入
            broker=self.broker,
            params={"indicators": indicators}
        )
        self.signal = StrategySignal(
            period=boll_params.get('period', 15),
            dev=boll_params.get('dev', 2),
            vol_ma_period=vol_ma_params.get('period', 5)
        )
        # 可选：初始化外部风控、sizer等

    def next(self):
        self.on_bar(self.data)

    def generate_signal(self, data, debug=False):
        signal = self.signal.get_signal(data, debug=debug)
        debug_info = {"signal": signal}
        return signal, debug_info

    def execute_order(self, signal, size, data, debug=False):
        # 这里实现backtrader下单逻辑
        # ...existing code...
        return {
            "symbol": data.get("symbol"),
            "action": signal,
            "size": size,
            "price": data.get("close"),
            "date": data.get("date"),
        }