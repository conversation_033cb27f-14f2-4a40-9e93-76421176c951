#!/usr/bin/env python3
"""
因子研究框架独立测试脚本

不依赖外部数据源，使用模拟数据测试框架功能。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

from factor_research.factors.momentum import MomentumFactor, RSIFactor
from factor_research.core.analyzer import FactorAnalyzer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_mock_factor_data():
    """创建模拟因子数据"""
    # 创建日期和资产
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    assets = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    # 创建MultiIndex
    index = pd.MultiIndex.from_product([dates, assets], names=['date', 'asset'])
    
    # 生成随机因子值
    np.random.seed(42)
    factor_values = np.random.normal(0, 1, len(index))
    
    # 创建因子数据Series
    factor_data = pd.Series(factor_values, index=index, name='test_factor')
    
    return factor_data

def create_mock_price_data():
    """创建模拟价格数据"""
    dates = pd.date_range(start='2023-01-01', end='2024-01-31', freq='D')  # 扩展日期范围
    assets = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    # 创建MultiIndex
    index = pd.MultiIndex.from_product([dates, assets], names=['date', 'asset'])
    
    # 生成随机价格数据
    np.random.seed(42)
    prices = []
    
    for asset in assets:
        # 为每个资产生成价格序列
        returns = np.random.normal(0.001, 0.02, len(dates))
        asset_prices = 100 * np.exp(np.cumsum(returns))
        prices.extend(asset_prices)
    
    # 创建价格数据Series
    price_data = pd.Series(prices, index=index, name='close')
    
    return price_data

def test_factor_calculation():
    """测试因子计算"""
    logger.info("Testing factor calculation...")
    
    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    assets = ['AAPL', 'MSFT', 'GOOGL']
    
    # 为每个资产创建价格数据
    data = {}
    np.random.seed(42)
    
    for asset in assets:
        returns = np.random.normal(0.001, 0.02, len(dates))
        prices = 100 * np.exp(np.cumsum(returns))
        
        asset_data = pd.DataFrame({
            'open': prices * (1 + np.random.normal(0, 0.005, len(dates))),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.01, len(dates)))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.01, len(dates)))),
            'close': prices,
            'volume': np.random.lognormal(15, 0.5, len(dates))
        }, index=dates)
        
        data[asset] = asset_data
    
    # 测试动量因子
    momentum_factor = MomentumFactor(lookback_period=20, skip_period=1)
    
    # 为每个资产计算因子
    factor_results = []
    
    for asset, asset_data in data.items():
        try:
            factor_values = momentum_factor.calculate(asset_data)
            # 添加资产标识
            factor_values.index = pd.MultiIndex.from_product(
                [factor_values.index, [asset]], 
                names=['date', 'asset']
            )
            factor_results.append(factor_values)
        except Exception as e:
            logger.error(f"Error calculating factor for {asset}: {e}")
    
    if factor_results:
        # 合并所有因子数据
        combined_factor = pd.concat(factor_results)
        combined_factor = combined_factor.reorder_levels(['date', 'asset']).sort_index()
        
        logger.info(f"Factor calculation successful: {len(combined_factor)} observations")
        logger.info(f"Factor data sample:\n{combined_factor.head(10)}")
        
        return combined_factor
    else:
        logger.error("No factor data calculated")
        return pd.Series()

def test_factor_analysis():
    """测试因子分析"""
    logger.info("Testing factor analysis...")
    
    # 创建模拟因子数据
    factor_data = create_mock_factor_data()
    
    # 初始化分析器（不使用数据适配器）
    analyzer = FactorAnalyzer(data_adapter=None)
    
    try:
        # 分析因子
        analysis_results = analyzer.analyze_factor(
            factor_data=factor_data,
            periods=[1, 5, 10],
            quantiles=5,
            max_loss=0.35
        )
        
        logger.info("Factor analysis completed successfully")
        logger.info(f"Analysis results keys: {list(analysis_results.keys())}")
        
        # 检查各项分析结果
        for key, value in analysis_results.items():
            if isinstance(value, dict):
                logger.info(f"{key} contains: {list(value.keys())}")
            else:
                logger.info(f"{key}: {type(value)}")
        
        return analysis_results
        
    except Exception as e:
        logger.error(f"Factor analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_rsi_factor():
    """测试RSI因子"""
    logger.info("Testing RSI factor...")
    
    # 创建测试数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    # 生成价格数据
    returns = np.random.normal(0.001, 0.02, len(dates))
    prices = 100 * np.exp(np.cumsum(returns))
    
    test_data = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.005, len(dates))),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, len(dates)))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, len(dates)))),
        'close': prices,
        'volume': np.random.lognormal(15, 0.5, len(dates))
    }, index=dates)
    
    # 创建RSI因子
    rsi_factor = RSIFactor(period=14)
    
    try:
        # 计算RSI
        rsi_values = rsi_factor.calculate(test_data)
        
        logger.info(f"RSI calculation successful: {len(rsi_values)} values")
        logger.info(f"RSI range: {rsi_values.min():.2f} - {rsi_values.max():.2f}")
        logger.info(f"RSI mean: {rsi_values.mean():.2f}")
        
        return rsi_values
        
    except Exception as e:
        logger.error(f"RSI calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return pd.Series()

def test_simple_ic_calculation():
    """测试简化的IC计算"""
    logger.info("Testing simple IC calculation...")
    
    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    assets = ['AAPL', 'MSFT', 'GOOGL']
    
    # 创建因子数据和收益数据
    np.random.seed(42)
    data_list = []
    
    for date in dates:
        for asset in assets:
            factor_value = np.random.normal(0, 1)
            return_1d = np.random.normal(0.001, 0.02)
            return_5d = np.random.normal(0.005, 0.05)
            
            data_list.append({
                'date': date,
                'asset': asset,
                'factor': factor_value,
                '1D': return_1d,
                '5D': return_5d,
                'factor_quantile': np.random.randint(1, 6)
            })
    
    # 创建DataFrame
    factor_data_clean = pd.DataFrame(data_list)
    factor_data_clean = factor_data_clean.set_index(['date', 'asset'])
    
    # 初始化分析器
    analyzer = FactorAnalyzer(data_adapter=None)
    
    try:
        # 计算IC
        ic_results = analyzer._calculate_simple_ic(factor_data_clean)
        
        logger.info("IC calculation successful")
        logger.info(f"IC results shape: {ic_results.shape}")
        logger.info(f"IC statistics:\n{ic_results.describe()}")
        
        return ic_results
        
    except Exception as e:
        logger.error(f"IC calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def main():
    """主测试函数"""
    logger.info("Starting standalone factor research framework tests...")
    
    try:
        # 测试1: 因子计算
        logger.info("\n" + "="*50)
        logger.info("TEST 1: Factor Calculation")
        logger.info("="*50)
        factor_data = test_factor_calculation()
        
        # 测试2: RSI因子
        logger.info("\n" + "="*50)
        logger.info("TEST 2: RSI Factor")
        logger.info("="*50)
        rsi_data = test_rsi_factor()
        
        # 测试3: 简化IC计算
        logger.info("\n" + "="*50)
        logger.info("TEST 3: Simple IC Calculation")
        logger.info("="*50)
        ic_results = test_simple_ic_calculation()
        
        # 测试4: 因子分析
        logger.info("\n" + "="*50)
        logger.info("TEST 4: Factor Analysis")
        logger.info("="*50)
        analysis_results = test_factor_analysis()
        
        logger.info("\n" + "="*50)
        logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("Framework is working correctly without alphalens.")
        logger.info("="*50)
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
