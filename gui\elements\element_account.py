from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QListWidget, QTextEdit, QPushButton, QDialog, QMessageBox
)
import json

class ElementAccount(QWidget):
    def __init__(self, parent, account_manage):
        super().__init__(parent)
        self.parent = parent
        self.account_manage = account_manage
        self.accounts = {}
        self.account_list = None
        self.account_info_text = None
        self._init_ui()

    def _init_ui(self):
        layout = QVBoxLayout()
        # 账户列表
        layout.addWidget(QLabel("账户列表"))
        self.account_list = QListWidget()
        self._load_account()
        self.account_list.currentTextChanged.connect(self._on_select_account)
        layout.addWidget(self.account_list)
        # 账户信息
        layout.addWidget(QLabel("账户信息"))
        self.account_info_text = QTextEdit()
        self.account_info_text.setReadOnly(True)
        layout.addWidget(self.account_info_text)
        # 操作按钮
        btn_hbox = QHBoxLayout()
        btn_create = QPushButton("创建账户")
        btn_config = QPushButton("配置账户")
        btn_delete = QPushButton("删除账户")
        btn_hbox.addWidget(btn_create)
        btn_hbox.addWidget(btn_config)
        btn_hbox.addWidget(btn_delete)
        layout.addLayout(btn_hbox)
        btn_create.clicked.connect(self._on_create_account)
        btn_config.clicked.connect(self._on_config_account)
        btn_delete.clicked.connect(self._on_delete_account)
        self.setLayout(layout)

    def _load_account(self):
        self.accounts.clear()
        self.account_list.clear()
        account_db = self.account_manage.db
        try:
            df = account_db.conn.execute("SELECT account_id, info_json FROM accounts").fetchall()
            for row in df:
                account_id = row[0]
                info_json = row[1]
                self.account_list.addItem(account_id)
                try:
                    info = json.loads(info_json)
                except Exception:
                    print(f"[DEBUG] 解析账户信息JSON异常: {info_json}")
                    info = {}
                self.accounts[account_id] = info
        except Exception as e:
            print(f"[DEBUG] 读取accounts表异常: {e}")

    def _on_select_account(self, account_id):
        info = self.accounts.get(account_id, {})
        self.account_info_text.setText(json.dumps(info, ensure_ascii=False, indent=2))
        # 设置当前账户，便于后续获取
        if hasattr(self.account_manage, "set_current_account"):
            self.account_manage.set_current_account(account_id)

    def _on_create_account(self):
        from PyQt5.QtWidgets import QInputDialog
        account_name, ok = QInputDialog.getText(self, "创建账户", "请输入账户名称")
        if ok and account_name:
            try:
                self.account_manage._create_account(account_name)
                self._load_account()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"创建账户失败: {e}")

    def _on_config_account(self):
        account_id = self.account_list.currentItem().text() if self.account_list.currentItem() else None
        if not account_id:
            QMessageBox.information(self, "提示", "请先选中一个账户")
            return
        # 弹出配置账户对话框
        from gui.panels.panel_config_trade import PanelConfigTrade
        dlg = QDialog(self)
        dlg.setWindowTitle(f"配置账户 - {account_id}")
        dlg.resize(1200, 800)
        panel = PanelConfigTrade(dlg, account_manage=self.account_manage)
        vbox = QVBoxLayout(dlg)
        vbox.addWidget(panel)
        dlg.setLayout(vbox)
        result = dlg.exec_()
        # 配置完成后刷新账户和股票池信息
        if result == QDialog.Accepted:
            self._load_account()

    def _on_delete_account(self):
        account_id = self.account_list.currentItem().text() if self.account_list.currentItem() else None
        if not account_id:
            return
        try:
            self.account_manage._delete_account(account_id)
            self._load_account()
            self.account_info_text.clear()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"删除账户失败: {e}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"删除账户失败: {e}")

