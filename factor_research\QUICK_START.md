# 单因子研究框架快速开始指南

## 1. 安装和配置

### 1.1 安装依赖

```bash
# 安装核心依赖
pip install alphalens pandas numpy matplotlib seaborn scipy

# 可选：机器学习扩展
pip install scikit-learn xgboost
```

### 1.2 配置文件

创建 `factor_research/config/factor_config.yaml`:

```yaml
# 数据源配置
data_source:
  database_path: "data/quant_data.duckdb"
  price_table: "stock_daily"
  fundamental_table: "stock_fundamental"
  cache_enabled: true
  cache_size: 1000

# 因子计算配置
factor_calculation:
  default_lookback: 252
  min_periods: 20
  max_loss_rate: 0.35
  parallel_enabled: true
  max_workers: 4

# Alphalens分析配置
alphalens_settings:
  periods: [1, 5, 10, 20]
  quantiles: 5
  max_loss: 0.35
  zero_aware: false

# 可视化配置
visualization:
  figure_size: [12, 8]
  save_format: "png"
  dpi: 300
  style: "seaborn"
```

## 2. 基本使用示例

### 2.1 简单因子分析

```python
from factor_research import FactorResearch
from database.db_manage import DBManage
from portfolio.account.account_manage import AccountManage

# 初始化系统
account_manage = AccountManage()
db_manage = DBManage(account_manage)

# 创建因子研究实例
research = FactorResearch(
    config_path="factor_research/config/factor_config.yaml",
    db_manage=db_manage
)

# 定义研究参数
assets = ["000001", "000002", "600000", "600036", "000858"]
start_date = "2022-01-01"
end_date = "2023-12-31"

# 计算动量因子
momentum_factor = research.calculate_factor(
    factor_name="momentum_20d",
    assets=assets,
    start_date=start_date,
    end_date=end_date
)

print("动量因子计算完成:")
print(momentum_factor.head())

# 分析因子有效性
analysis_results = research.analyze_factor(
    factor_data=momentum_factor,
    periods=[1, 5, 10, 20],
    quantiles=5
)

print("\n因子分析结果:")
print("IC均值:", analysis_results['ic_analysis'].mean())
print("IC标准差:", analysis_results['ic_analysis'].std())

# 生成分析报告
report_path = research.generate_report(
    analysis_results, 
    output_path="reports/momentum_20d_analysis.html"
)
print(f"\n分析报告已生成: {report_path}")

# 绘制分析图表
research.plot_factor_analysis(
    analysis_results, 
    save_path="plots/momentum_20d/"
)
print("分析图表已保存到 plots/momentum_20d/")
```

### 2.2 批量因子分析

```python
# 批量分析多个因子
factor_names = [
    "momentum_20d",
    "rsi_14", 
    "volatility_20d",
    "pe_ratio",
    "roe"
]

# 批量计算和分析
batch_results = research.batch_analyze_factors(
    factor_names=factor_names,
    assets=assets,
    start_date=start_date,
    end_date=end_date,
    periods=[1, 5, 10, 20],
    quantiles=5
)

# 比较因子表现
print("\n因子表现比较:")
for factor_name, results in batch_results.items():
    ic_mean = results['ic_analysis'].mean().mean()
    print(f"{factor_name}: IC均值 = {ic_mean:.4f}")

# 生成批量分析报告
batch_report_path = research.generate_batch_report(
    batch_results,
    output_path="reports/batch_factor_analysis.html"
)
print(f"\n批量分析报告已生成: {batch_report_path}")
```

### 2.3 自定义因子示例

```python
from factor_research.factors.base import BaseFactor
import pandas as pd
import numpy as np

class CustomMomentumFactor(BaseFactor):
    """
    自定义动量因子：结合价格和成交量的动量
    """
    
    def __init__(self, price_window=20, volume_window=10):
        super().__init__(
            name=f"custom_momentum_{price_window}_{volume_window}",
            description=f"价格{price_window}日+成交量{volume_window}日动量因子"
        )
        self.price_window = price_window
        self.volume_window = volume_window
    
    def calculate(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        计算自定义动量因子
        """
        price_window = params.get('price_window', self.price_window)
        volume_window = params.get('volume_window', self.volume_window)
        
        # 价格动量
        price_momentum = data['close'].groupby(level=1).pct_change(price_window)
        
        # 成交量动量
        volume_momentum = data['volume'].groupby(level=1).pct_change(volume_window)
        
        # 组合动量（等权重）
        combined_momentum = 0.7 * price_momentum + 0.3 * volume_momentum
        
        return combined_momentum
    
    def get_required_data(self):
        return ['close', 'volume']

# 注册自定义因子
research.register_factor(CustomMomentumFactor)

# 使用自定义因子
custom_factor = research.calculate_factor(
    factor_name="custom_momentum_20_10",
    assets=assets,
    start_date=start_date,
    end_date=end_date
)

# 分析自定义因子
custom_results = research.analyze_factor(custom_factor)
print("\n自定义因子分析结果:")
print("IC均值:", custom_results['ic_analysis'].mean())
```

## 3. 高级功能示例

### 3.1 因子组合分析

```python
# 计算多个因子
factors_data = {}
factor_names = ["momentum_20d", "rsi_14", "volatility_20d"]

for factor_name in factor_names:
    factors_data[factor_name] = research.calculate_factor(
        factor_name=factor_name,
        assets=assets,
        start_date=start_date,
        end_date=end_date
    )

# 线性组合因子
combined_factor = research.combine_factors(
    factor_data_dict=factors_data,
    method="linear",
    weights={"momentum_20d": 0.5, "rsi_14": 0.3, "volatility_20d": 0.2}
)

# 分析组合因子
combined_results = research.analyze_factor(combined_factor)
print("\n组合因子分析结果:")
print("IC均值:", combined_results['ic_analysis'].mean())
```

### 3.2 行业中性化分析

```python
# 获取行业分类数据
industry_data = research.data_adapter.get_industry_classification(assets)

# 行业中性化因子分析
neutral_results = research.analyze_factor(
    factor_data=momentum_factor,
    groupby=industry_data,  # 按行业分组
    periods=[1, 5, 10, 20],
    quantiles=5
)

print("\n行业中性化分析结果:")
print("分组IC分析:", neutral_results['ic_by_group'])
```

### 3.3 因子衰减分析

```python
# 分析因子在不同持有期的表现
decay_analysis = research.factor_decay_analysis(
    factor_data=momentum_factor,
    periods=list(range(1, 21))  # 1-20日持有期
)

print("\n因子衰减分析:")
print(decay_analysis['ic_decay'])

# 绘制衰减图
research.plot_factor_decay(decay_analysis, save_path="plots/momentum_decay.png")
```

## 4. 性能优化技巧

### 4.1 数据缓存

```python
# 启用数据缓存
research.enable_cache(cache_size=1000)

# 预加载数据
research.preload_data(
    assets=assets,
    start_date=start_date,
    end_date=end_date,
    data_types=['price', 'fundamental']
)
```

### 4.2 并行计算

```python
# 启用并行计算
research.enable_parallel(max_workers=4)

# 并行计算多个因子
parallel_results = research.parallel_calculate_factors(
    factor_names=["momentum_20d", "rsi_14", "volatility_20d"],
    assets=assets,
    start_date=start_date,
    end_date=end_date
)
```

### 4.3 增量更新

```python
# 增量更新因子数据
new_factor_data = research.incremental_update_factor(
    factor_name="momentum_20d",
    assets=assets,
    last_update_date="2023-12-31",
    current_date="2024-01-31"
)
```

## 5. 常见问题和解决方案

### 5.1 数据缺失处理

```python
# 设置数据缺失处理策略
research.set_missing_data_strategy(
    method="forward_fill",  # 前向填充
    max_missing_ratio=0.1   # 最大缺失比例
)
```

### 5.2 异常值处理

```python
# 设置异常值处理
research.set_outlier_treatment(
    method="winsorize",     # 缩尾处理
    lower_percentile=0.01,  # 下分位数
    upper_percentile=0.99   # 上分位数
)
```

### 5.3 内存优化

```python
# 设置内存优化选项
research.set_memory_options(
    chunk_size=1000,        # 分块处理大小
    use_float32=True,       # 使用float32节省内存
    clear_cache_interval=100 # 缓存清理间隔
)
```

## 6. 输出文件说明

### 6.1 分析报告结构

```
reports/
├── factor_analysis_report.html     # 主报告文件
├── ic_analysis.csv                 # IC分析数据
├── quantile_returns.csv            # 分位数收益数据
├── turnover_analysis.csv           # 换手率分析数据
└── summary_statistics.csv          # 汇总统计数据
```

### 6.2 图表文件结构

```
plots/
├── ic_time_series.png              # IC时序图
├── cumulative_returns.png          # 累计收益图
├── quantile_returns_heatmap.png    # 分位数收益热力图
├── factor_distribution.png         # 因子分布图
└── turnover_analysis.png           # 换手率分析图
```

## 7. 下一步学习

1. **深入学习Alphalens**: 了解更多高级分析功能
2. **因子工程**: 学习更复杂的因子构造方法
3. **多因子模型**: 探索因子组合和权重优化
4. **风险管理**: 学习因子风险模型和风险归因
5. **实盘应用**: 将因子研究结果应用到实际交易策略

## 8. 技术支持

- **文档**: 查看完整API文档
- **示例**: 参考examples/目录下的更多示例
- **测试**: 运行tests/目录下的测试用例
- **社区**: 参与因子研究讨论和经验分享

这个快速开始指南提供了从基础使用到高级功能的完整示例，帮助用户快速上手因子研究框架。
