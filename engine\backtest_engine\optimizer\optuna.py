import optuna
from backtest.evaluator.evaluator import BaseEvaluator
class OptunaOptimizer:
    def __init__(self, strategy_class, data, evaluator: BaseEvaluator):
        """
        初始化优化器
        :param strategy_class: 策略类
        :param data: 回测数据
        :param evaluator: 评估器（如RollingEvaluator）
        """
        self.strategy_class = strategy_class
        self.data = data
        self.evaluator = evaluator

    def _objective_function(self, trial):
        """
        优化目标函数
        :param trial: Optuna的试验对象
        :return: 当前参数组合的评估得分
        """
        cur_param = {}
        for param, bounds in self.strategy_class.params_bound.items():
            low, high = bounds
            if isinstance(low, float) or isinstance(high, float):
                cur_param[param] = trial.suggest_float(param, low, high)
            else:
                cur_param[param] = trial.suggest_int(param, low, high)
        
        return self.evaluator.evaluate(self.strategy_class, self.data, cur_param)

    def _run(self, n_trials=100):
        """
        运行优化
        :param n_trials: 优化次数
        :return: 最佳参数组合
        """
        study = optuna.create_study(direction="maximize")
        study.optimize(self._objective_function, n_trials=n_trials)
        return study.best_params
