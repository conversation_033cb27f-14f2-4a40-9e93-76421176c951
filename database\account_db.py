import duckdb
import os
import json
from common.config import DATA_BASE_DIR

class AccountDB(object):
    """
    专门负责账户数据的存取、数据库操作（基于duckdb）
    """
    def __init__(self, db_path=None):
        if db_path is None:
            db_path = os.path.join(DATA_BASE_DIR, "account_data.duckdb")
        self.db_path = db_path
        self.conn = duckdb.connect(database=db_path, read_only=False)
        self._init_table()

    def _init_table(self):
        """
        初始化账户表结构
        """
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS accounts (
            account_id VARCHAR PRIMARY KEY,
            info_json VARCHAR
        )
        """)

    def save_account(self, account_id, account_data):
        """
        保存账户数据到数据库（如存在则更新，否则插入）
        推荐用json存储info_json字段，便于结构灵活扩展。
        """
        info_json = json.dumps(account_data, ensure_ascii=False)
        self.conn.execute("""
            INSERT INTO accounts (account_id, info_json)
            VALUES (?, ?)
            ON CONFLICT(account_id) DO UPDATE SET
                info_json=excluded.info_json
        """, [account_id, info_json])

    def load_account(self, account_id):
        """
        加载账户数据
        """
        row = self.conn.execute(
            "SELECT info_json FROM accounts WHERE account_id=?", [account_id]
        ).fetchone()
        if not row:
            return None
        return json.loads(row[0])

    def delete_account(self, account_id):
        """
        删除账户数据
        """
        self.conn.execute("DELETE FROM accounts WHERE account_id=?", [account_id])

    def list_accounts(self):
        """
        列出所有账户id
        """
        df = self.conn.execute("SELECT account_id FROM accounts").df()
        return df["account_id"].tolist() if not df.empty else []


    def update_account(self, account_id, update_data):
        """
        更新账户部分字段（只更新传入的字段）
        """
        account = self.load_account(account_id)
        if not account:
            return
        account.update(update_data)
        self.save_account(account_id, account)

    def close(self):
        self.conn.close()