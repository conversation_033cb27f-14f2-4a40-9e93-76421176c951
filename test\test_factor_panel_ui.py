#!/usr/bin/env python3
"""
测试优化后的因子研究面板界面
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from gui.panels.panel_factor_research import PanelFactorResearch
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("因子研究面板界面测试")
            self.setGeometry(100, 100, 1200, 800)
            
            # 创建中央窗口
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout()
            central_widget.setLayout(layout)
            
            # 添加因子研究面板
            self.factor_panel = PanelFactorResearch()
            layout.addWidget(self.factor_panel)
            
    def main():
        app = QApplication(sys.argv)
        
        # 设置应用样式
        app.setStyle('Fusion')
        
        window = TestWindow()
        window.show()
        
        print("界面优化内容:")
        print("1. 股票池选择区域改为水平布局，更加紧凑")
        print("2. 添加了快速选择按钮，可以选择预设股票池")
        print("3. 时间范围组改为水平布局，减少垂直空间")
        print("4. 控制按钮改为水平布局，添加了图标和样式")
        print("5. 减少了各组件的边距和高度限制")
        print("\n请测试快速选择功能和整体界面布局...")
        
        sys.exit(app.exec_())
        
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保因子研究框架已正确安装")
