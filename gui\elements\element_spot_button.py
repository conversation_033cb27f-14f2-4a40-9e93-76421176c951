from database.db_manage import DBManage
from portfolio.account.account_manage import AccountManage

class ElementSpotButton:
    def __init__(self, name, x, y, width, height):
        self.name = name
        self.x = x
        self.y = y
        self.width = width
        self.height = height

    def click(self):
        print(f"{self.name} button clicked at ({self.x}, {self.y})")
        if self.name == "下载行情数据":
            print("正在下载实时行情数据并更新数据库...")
            account_manage = AccountManage()
            db_manage = DBManage(account_manage)
            db_manage.update_realtime_once()
            print("实时行情数据已更新。")
    
    def render(self):
        print(f"Rendering {self.name} button at ({self.x}, {self.y}) with size ({self.width}x{self.height})")