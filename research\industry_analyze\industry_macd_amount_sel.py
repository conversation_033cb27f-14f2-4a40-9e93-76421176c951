import os 
import pandas as pd
from datetime import datetime, timedelta
from data_gen.data_generator_em import DataGeneratorEM
from cal_lib.cal_lib import CalLib
import heapq  # Add this import
import yaml  # Add this import

class IndustryMACDAmountSelector:
    def __init__(self, industries, industry_data, analyze_period):
        self.industries = industries
        self.industry_data = industry_data
        self.params = self._load_params(analyze_period)
        self.industry_predict_pct = {}
        self.df = None

    def _load_params(self, analyze_period):
        """Load parameters from YAML file"""
        params = []
        print("analyze_period:", analyze_period)
        file_path = f'data/output/{analyze_period}_params_accuracy.yaml'
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                for item in data:
                    params.append(item['params'])  # Extract parameters from YAML
        return params

    def _signal_cal(self):
        fastperiod = self.params['fastperiod']
        slowperiod = self.params['slowperiod']
        signalperiod = self.params['signalperiod']
        threadhold = self.params['threadhold']
        amount_window = self.params['amount_window']
        cal_lib = CalLib()
        self.df = cal_lib._macd_cal(self.df, fastperiod=fastperiod, slowperiod=slowperiod, signalperiod=signalperiod)
        self.df = cal_lib._macd_diff_cal(self.df)
        self.df = cal_lib._amount_increase(self.df, amount_window)
        self.df['positive_signal'] = 0  # Default to 0
        self.df['positive_signal'] = (    (self.df['delta_macd'] > 0)
                                      & (self.df['macd_hist'] < 0)
                                      & (self.df['macd_hist'] > threadhold)
                                      & (self.df['amount_incr'])
                                      )

    def _update_df(self, industry):
        self.df = self.industry_data[industry]
        self._signal_cal()

    def _cal_pred_pct(self, industry):
        self._update_df(industry)
        """Count and print the percentage change for the week following a positive signal"""
        predict_cnt = 0
        positive_cnt = 0
        correct_cnt = 0
        wrong_cnt = 0
        for i in range(len(self.df)-2):
            if(self.df['change'].iloc[i]>0):
                positive_cnt += 1
            if self.df['positive_signal'].iloc[i]:
                predict_cnt += 1
                change_next = self.df['change'].iloc[i+1]
                if change_next > 0:
                    correct_cnt += 1
                else:
                    wrong_cnt += 1
        if(predict_cnt != 0):
            predict_pct = correct_cnt / predict_cnt
            # print(f"Industry: {industry}, Total: {predict_cnt}, Correct: {correct_cnt}, Wrong: {wrong_cnt}, Correct %: {predict_pct:.2%}")
        else:
            predict_pct = 0
        
        self.industry_predict_pct[industry] = predict_pct
        return (predict_cnt, correct_cnt)

    def _parm_optimize(self, analyze_period):
        """Iterate over parameters to maximize correct_pct"""
        best_params = None
        best_correct_pct = 0
        threadhold = -20
        amount_window = 3
        for fastperiod in range(6, 10):
            for slowperiod in range(20, 30):
                for signalperiod in range(6, 10):
                    self.params = {
                        'fastperiod': fastperiod,
                        'slowperiod': slowperiod,
                        'signalperiod': signalperiod,
                        'threadhold': threadhold,
                        'amount_window': amount_window
                    }
                    total_pred_cnt = 0
                    total_right_cnt = 0
                    for industry in self.industries:
                        (pred_cnt, right_cnt) = self._cal_pred_pct(industry)
                        total_pred_cnt += pred_cnt
                        total_right_cnt += right_cnt
                    correct_pct = total_right_cnt / total_pred_cnt if total_pred_cnt != 0 else 0
                    if correct_pct > best_correct_pct:
                        best_correct_pct = correct_pct
                        best_params = self.params.copy()

        self.params = best_params # Only keep the best parameters

        # Save the parameters and their corresponding industry prediction accuracy to a YAML file
        params_with_accuracy = [{'params': best_params, 'accuracy': self.industry_predict_pct}]
        with open(f'data/output/{analyze_period}_params_accuracy.yaml', 'w', encoding='utf-8') as yaml_file:
            yaml.dump(params_with_accuracy, yaml_file, allow_unicode=True)


    def _output_predict(self):
        output_data = []
        out_path = f"data/output/industry_predict"
        os.makedirs(out_path, exist_ok=True)
        for industry in self.industries:
            pred_result = self._cal_pred_pct(industry)
            self.df.to_csv(f"{out_path}/{industry}.csv", index=False, encoding='utf_8_sig')


