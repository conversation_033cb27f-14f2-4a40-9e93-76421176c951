import akshare as ak
import pandas as pd
import matplotlib.pyplot as plt

def fetch_stock_data(code, start_date, end_date):
    df = ak.stock_zh_a_hist(symbol=code, period='daily', start_date=start_date, end_date=end_date)
    df.rename(columns={
        "股票代码": "code",
        '日期': 'date',
        '开盘': 'open',
        '最高': 'high',
        '最低': 'low',
        '收盘': 'close',
        '成交量': 'volume',
        '成交额': 'amount'
    }, inplace=True)
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    df = df.sort_index()
    return df

def calculate_volatility(df):
    df['volatility'] = (df['high'] - df['low']) / df['low'] * 100
    df['price_change'] = df['close'].pct_change() * 100
    return df

def plot_data(df):
    fig, ax1 = plt.subplots()

    ax1.set_xlabel('Date')
    ax1.set_ylabel('Volatility (%)', color='tab:blue')
    ax1.plot(df.index, df['volatility'], color='tab:blue', label='Volatility')
    ax1.tick_params(axis='y', labelcolor='tab:blue')

    ax2 = ax1.twinx()
    ax2.set_ylabel('Price Change (%)', color='tab:red')
    ax2.plot(df.index, df['price_change'], color='tab:red', label='Price Change')
    ax2.tick_params(axis='y', labelcolor='tab:red')

    fig.tight_layout()
    plt.title('Daily Volatility and Price Change')
    plt.show()

if __name__ == '__main__':
    code = '688536'
    start_date = '20240120'
    end_date = '20250227'
    df = fetch_stock_data(code, start_date, end_date)
    df = calculate_volatility(df)
    plot_data(df)
