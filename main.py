from PyQt5.QtWidgets import QApplication
from gui.mainframe import <PERSON><PERSON>rame
from database.db_manage import DBManage
from portfolio.account.account_manage import AccountManage
from portfolio.position.position_manager import PositionManage
from engine.trade_engine.trade_engine import TradeEngine
from engine.trade_engine.trade_broker import TradeBroker
from plot.trade_plot import TradePlot
import sys

if __name__ == "__main__":
    account_manager = AccountManage()
    db_manager = DBManage(account_manager)
    trade_broker = TradeBroker()
    position_manager = PositionManage(broker=trade_broker)
    trade_ploter = TradePlot(db_manage=db_manager)
    trade_engine = TradeEngine(
        trade_broker=trade_broker,
        position_manager=position_manager,
        db_manage=db_manager,
        trade_plot=trade_ploter
    )

    # 启动数据库后台更新功能
    db_manager.main_loop()

    app = QApplication(sys.argv)
    frame = MainFrame(
        position_manager=position_manager,
        account_manage=account_manager,
        trade_engine=trade_engine,
        db_manage=db_manager,
        trade_ploter=trade_ploter
    )
    frame.show()
    sys.exit(app.exec_())