import backtrader as bt
from .base import BaseProportionSizer

class BtSizer(bt.<PERSON><PERSON>, BaseProportionSizer):
    params = (
        ('buy_prop', 0.1),
        ('sell_prop', 0.1),
        ('max_position_value', None),
        ('min_size', 100),
        ('round_lot', 100),
    )

    def __init__(self):
        # backtrader会自动设置self.p
        BaseProportionSizer.__init__(
            self,
            buy_prop=self.p.buy_prop,
            sell_prop=self.p.sell_prop,
            max_position_value=self.p.max_position_value,
            min_size=self.p.min_size,
            round_lot=self.p.round_lot,
        )

    def _getsizing(self, comminfo, cash, data, isbuy):
        price = data.close[0]
        position = self.broker.getposition(data)
        position_size = position.size
        if isbuy:
            return self.calc_buy_size(cash, price)
        else:
            return self.calc_sell_size(position_size)
