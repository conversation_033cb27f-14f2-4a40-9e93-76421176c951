import backtrader as bt

class CalmarRatioAnalyzer(bt.Analyzer):
    def __init__(self):
        self.returns = []
        self.highwatermark = -float('inf')
        self.maxdrawdown = 0

    def next(self):
        # 当前收益
        total_value = self.strategy.broker.getvalue()
        self.returns.append(total_value)

        # 更新高点和最大回撤
        self.highwatermark = max(self.highwatermark, total_value)
        drawdown = (total_value - self.highwatermark) / self.highwatermark
        self.maxdrawdown = min(self.maxdrawdown, drawdown)

    def get_analysis(self):
        if len(self.returns) < 2:
            return {'calmar': None}

        # 总体收益率
        start_value = self.returns[0]
        end_value = self.returns[-1] + self.strategy.broker.getcash()
        total_return = (end_value - start_value) / start_value

        # 年化收益率（按252交易日估算）
        periods = len(self.returns)
        annual_return = (1 + total_return) ** (252 / periods) - 1

        # 最大回撤是负值，取绝对值
        max_dd = abs(self.maxdrawdown) if self.maxdrawdown != 0 else 1e-6

        # Calmar比率
        calmar = annual_return / max_dd
        return calmar if max_dd != 0 else None