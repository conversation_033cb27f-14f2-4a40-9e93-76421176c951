"""
This code is used for selecting stock buy points.
"""

import os
import talib
import akshare as ak
import pandas as pd
from datetime import datetime
from data_gen.data_generator_em import DataGeneratorEM
from stock_tech_analyze import StockTechAnalyze
from cal_lib.cal_lib import CalLib

class StockAnalyzer:
    def __init__(self):
        self.stock_cons = None
        self.selector = None

    def _load_data(self, df):
        pass

    def _cal_signal(self, df):
        df = self.strategy._cal_signal(df)
        return df

    def _load_industry_stock_list(self, industry):
        """获取行业股票列表"""
        self.stock_cons = self.generator._read_csv(f"data/industries/industry_cons/{industry}.csv")

    def _select(self, selector):
        self.selector = selector(self.stock_cons)
        scand = self.selector._output_candidate()
        return scand

    def stock_analyze(self):
        """股票分析"""
        pass