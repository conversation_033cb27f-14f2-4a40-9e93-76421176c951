import httpx
from volcenginesdkarkruntime import Ark
import pandas as pd
from data_gen.data_generator_em import DataGeneratorEM
import yaml  # Added import for YAML handling

class stock_fundamental_analyzer:
    """根据基本面筛选股票"""
    def __init__(self):
        self.client = None
    
    def _agent_init(self):
        self.client = Ark(    
            # The output time of the reasoning model is relatively long. Please increase the timeout period.
            api_key= os.environ.get("ARK_API_KEY"),
            timeout=httpx.Timeout(timeout=1800),
        )

    def _stock_recommand_by_ai(self, date, industry, period):
        """根据行业筛选股票"""
        # 2. 调用Ark的API
        stream = self.client.chat.completions.create(
            model="ep-20250212220301-s8nt5",
            messages=[
                {"role": "user", "content": f"请推荐一些{industry}行业的股票, 交易周期为{period}，并给出理由。"},
            ],
            stream=True
        )
        # 3. 处理返回结果
        recommendations = []
        for chunk in stream:
            if not chunk.choices:
                continue
            if chunk.choices[0].delta.reasoning_content:
                content = chunk.choices[0].delta.reasoning_content
            else:
                content = chunk.choices[0].delta.content
            # print(content, end="")
            recommendations.append(content)
        print(recommendations)

        # 4. 写入到yaml文件
        output_data = {
            "date": date,
            "industry": industry,
            "period": period,
            "recommendations": recommendations
        }
        with open(f"stock_recommendations_{date}.yaml", "w", encoding="utf-8") as file:
            yaml.dump(output_data, file, allow_unicode=True)
