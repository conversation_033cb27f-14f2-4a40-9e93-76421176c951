#!/usr/bin/env python3
"""
测试标题显示效果

验证因子研究面板标题是否能正确显示，不被边框遮挡。
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_title_display():
    """测试标题显示"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        from gui.panels.panel_factor_research import PanelFactorResearch
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle("标题显示测试")
        window.setGeometry(100, 100, 400, 600)
        
        # 创建因子研究面板
        panel = PanelFactorResearch()
        
        # 设置为中央组件
        window.setCentralWidget(panel)
        
        # 显示窗口
        window.show()
        
        print("✅ 标题显示测试窗口已打开")
        print("请检查以下标题是否完整显示，没有被边框遮挡：")
        print("  1. 主标题: '📈 因子研究'")
        print("  2. 因子配置: '📈 因子配置'")
        print("  3. 股票池: '🏢 股票池'")
        print("  4. 时间范围: '📅 时间范围'")
        print("按 Ctrl+C 或关闭窗口退出测试")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 标题显示测试失败: {e}")
        return False

if __name__ == "__main__":
    test_title_display()
