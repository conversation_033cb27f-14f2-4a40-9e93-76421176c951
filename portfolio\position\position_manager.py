from portfolio.risk.risk_manage import RiskManager


class PositionManage(object):
    def __init__(self, broker=None, risk_manager=None, max_total_ratio=1.0, symbol_max_ratio=None):
        # 如果未传入risk_manager，则自动实例化一个
        if risk_manager is None:
            risk_manager = RiskManager(max_total_ratio=max_total_ratio, symbol_max_ratio=symbol_max_ratio)
        self.risk_manager = risk_manager
        self.broker = broker

    def update_position(self, symbol, quantity, cost):
        """
        兼容接口，直接调用broker相关方法或忽略（如需扩展可实现）
        """
        pass  # 推荐直接通过broker维护持仓

    def adjust_position(self, symbol, delta_qty, price):
        """
        兼容接口，直接调用broker相关方法或忽略
        """
        pass

    def update_unrealized_pnl(self, symbol, market_price):
        """
        兼容接口，直接调用broker相关方法或忽略
        """
        pass

    def get_position(self, symbol):
        """
        查询单个标的持仓信息，直接通过broker获取
        """
        if self.broker:
            return self.broker.get_position(symbol)
        return {"quantity": 0, "cost": 0.0, "unrealized_pnl": 0.0}

    def get_all_positions(self):
        """
        返回所有持仓信息，直接通过broker获取
        """
        if self.broker:
            return self.broker.get_all_positions()
        return {}

    def check_risk(self, symbol, signal=None, position=None, data=None, broker=None):
        """
        调用风控管理器进行风控检查，返回是否允许操作
        signal参数的作用：
        - 表示当前操作的信号类型（如买入、卖出、加仓、减仓等），便于风控策略根据不同信号类型做差异化风控处理。
        - 例如：风控可对买入信号限制最大持仓，对卖出信号限制最小持仓，对不同信号类型设置不同风控规则。
        - 便于扩展更复杂的风控逻辑（如禁止某些信号、只允许部分信号等）。
        """
        if not self.risk_manager:
            return True  # 未配置风控则默认允许
        broker = broker or self.broker
        if broker:
            account_value = broker.get_value()
            total_position_value = broker._total_market_value()
            symbol_position = broker.get_position(symbol)
            symbol_position_value = symbol_position['quantity'] * data.get('close', 0) if data else 0
        else:
            print("警告：未配置broker，无法获取持仓信息，使用默认值")
            symbol_position = position or {"quantity": 0, "cost": 0.0}
            symbol_position_value = symbol_position["quantity"] * data.get('close', 0) if data else 0
            total_position_value = symbol_position_value
            account_value = total_position_value
        return self.risk_manager.check(
            symbol, signal, symbol_position_value, total_position_value, account_value
        )

    def get_max_position_value(self, symbol):
        """
        获取单标的最大持仓金额（实时），由风控配置决定
        """
        if self.risk_manager and self.broker:
            account_value = self.broker.get_value()
            max_ratio = getattr(self.risk_manager, "symbol_max_ratio", {}).get(symbol, 0.7)
            return account_value * max_ratio
        else:
            print("警告：未配置broker或risk_manager，无法获取最大持仓金额，返回0")
        return 0
