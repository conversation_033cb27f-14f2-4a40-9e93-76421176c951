"""
因子计算引擎

负责各种因子的计算，提供统一的因子计算接口。
支持因子注册、批量计算、并行计算等功能。
"""

from typing import List, Dict, Any, Optional, Type
import pandas as pd
import numpy as np
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from ..factors.base import BaseFactor, factor_registry
from .data_adapter import DataAdapter

logger = logging.getLogger(__name__)


class FactorEngine:
    """
    因子计算引擎，负责各种因子的计算
    
    主要功能:
    - 因子注册和管理
    - 单因子计算
    - 批量因子计算
    - 并行计算支持
    - 计算结果缓存
    - 性能监控
    """
    
    def __init__(self, data_adapter: DataAdapter, parallel_enabled: bool = False, max_workers: int = 4):
        """
        初始化因子计算引擎
        
        Parameters:
        -----------
        data_adapter : DataAdapter
            数据适配器对象
        parallel_enabled : bool, optional
            是否启用并行计算，默认False
        max_workers : int, optional
            最大工作线程数，默认4
        """
        self.data_adapter = data_adapter
        self.parallel_enabled = parallel_enabled
        self.max_workers = max_workers
        self._calculation_cache = {}
        self._performance_stats = {}
        
        # 注册默认因子
        self._register_default_factors()
        
    def register_factor(self, factor_class: Type[BaseFactor], **default_params) -> None:
        """
        注册新的因子类
        
        Parameters:
        -----------
        factor_class : Type[BaseFactor]
            继承自BaseFactor的因子类
        **default_params : dict
            默认参数
        """
        try:
            factor_registry.register(factor_class, **default_params)
            logger.info(f"Successfully registered factor: {factor_class.__name__}")
        except Exception as e:
            logger.error(f"Failed to register factor {factor_class.__name__}: {str(e)}")
            raise
            
    def get_available_factors(self) -> List[str]:
        """
        获取可用的因子列表
        
        Returns:
        --------
        List[str]
            可用因子名称列表
        """
        return factor_registry.list_factors()
        
    def get_factors_by_category(self, category: str) -> List[str]:
        """
        按分类获取因子列表
        
        Parameters:
        -----------
        category : str
            因子分类
            
        Returns:
        --------
        List[str]
            该分类下的因子名称列表
        """
        return factor_registry.get_factors_by_category(category)
        
    def get_factor_info(self, factor_name: str) -> Dict[str, Any]:
        """
        获取因子详细信息
        
        Parameters:
        -----------
        factor_name : str
            因子名称
            
        Returns:
        --------
        Dict[str, Any]
            因子信息
        """
        return factor_registry.get_factor_info(factor_name)
        
    def calculate_factor(self, factor_name: str, assets: List[str],
                        start_date: str, end_date: str, **params) -> pd.Series:
        """
        计算指定因子
        
        Parameters:
        -----------
        factor_name : str
            因子名称
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期，格式 'YYYY-MM-DD'
        end_date : str
            结束日期，格式 'YYYY-MM-DD'
        **params : dict
            因子计算参数
            
        Returns:
        --------
        pd.Series
            MultiIndex (date, asset) 的因子值
        """
        start_time = time.time()
        
        try:
            # 获取因子实例
            factor = factor_registry.get_factor(factor_name)
            
            # 检查缓存
            cache_key = self._generate_cache_key(factor_name, assets, start_date, end_date, params)
            if cache_key in self._calculation_cache:
                logger.debug(f"Using cached result for factor {factor_name}")
                return self._calculation_cache[cache_key]
            
            # 获取所需数据
            required_fields = factor.get_required_data()
            logger.info(f"Loading data for factor {factor_name}, required fields: {required_fields}")
            
            data = self.data_adapter.get_price_data(
                assets=assets,
                start_date=start_date,
                end_date=end_date,
                fields=required_fields
            )
            
            if data.empty:
                logger.warning(f"No data available for factor {factor_name}")
                return pd.Series(dtype=float, name=factor_name)
            
            # 计算因子
            logger.info(f"Calculating factor {factor_name}")
            factor_values = factor.calculate_with_validation(data, **params)
            
            # 设置因子名称
            factor_values.name = factor_name
            
            # 缓存结果
            self._calculation_cache[cache_key] = factor_values
            
            # 记录性能统计
            calculation_time = time.time() - start_time
            self._record_performance(factor_name, calculation_time, len(assets), len(factor_values))
            
            logger.info(f"Successfully calculated factor {factor_name} in {calculation_time:.2f}s")
            
            return factor_values
            
        except Exception as e:
            logger.error(f"Error calculating factor {factor_name}: {str(e)}")
            raise
            
    def calculate_multiple_factors(self, factor_names: List[str], assets: List[str],
                                 start_date: str, end_date: str, **params) -> Dict[str, pd.Series]:
        """
        批量计算多个因子
        
        Parameters:
        -----------
        factor_names : List[str]
            因子名称列表
        assets : List[str]
            股票代码列表
        start_date : str
            开始日期
        end_date : str
            结束日期
        **params : dict
            因子计算参数
            
        Returns:
        --------
        Dict[str, pd.Series]
            {factor_name: factor_values} 的字典
        """
        if self.parallel_enabled and len(factor_names) > 1:
            return self._calculate_factors_parallel(factor_names, assets, start_date, end_date, **params)
        else:
            return self._calculate_factors_sequential(factor_names, assets, start_date, end_date, **params)
            
    def _calculate_factors_sequential(self, factor_names: List[str], assets: List[str],
                                    start_date: str, end_date: str, **params) -> Dict[str, pd.Series]:
        """顺序计算多个因子"""
        results = {}
        
        for factor_name in factor_names:
            try:
                factor_values = self.calculate_factor(
                    factor_name=factor_name,
                    assets=assets,
                    start_date=start_date,
                    end_date=end_date,
                    **params
                )
                results[factor_name] = factor_values
                
            except Exception as e:
                logger.error(f"Error calculating factor {factor_name}: {str(e)}")
                # 继续计算其他因子
                continue
                
        return results
        
    def _calculate_factors_parallel(self, factor_names: List[str], assets: List[str],
                                  start_date: str, end_date: str, **params) -> Dict[str, pd.Series]:
        """并行计算多个因子"""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有计算任务
            future_to_factor = {
                executor.submit(
                    self.calculate_factor,
                    factor_name, assets, start_date, end_date, **params
                ): factor_name
                for factor_name in factor_names
            }
            
            # 收集结果
            for future in as_completed(future_to_factor):
                factor_name = future_to_factor[future]
                try:
                    factor_values = future.result()
                    results[factor_name] = factor_values
                    logger.info(f"Completed parallel calculation for factor {factor_name}")
                    
                except Exception as e:
                    logger.error(f"Error in parallel calculation for factor {factor_name}: {str(e)}")
                    continue
                    
        return results
        
    def _generate_cache_key(self, factor_name: str, assets: List[str], 
                          start_date: str, end_date: str, params: Dict) -> str:
        """生成缓存键"""
        assets_str = '-'.join(sorted(assets))
        params_str = '-'.join(f"{k}:{v}" for k, v in sorted(params.items()))
        return f"{factor_name}_{assets_str}_{start_date}_{end_date}_{params_str}"
        
    def _record_performance(self, factor_name: str, calculation_time: float, 
                          num_assets: int, num_records: int):
        """记录性能统计"""
        if factor_name not in self._performance_stats:
            self._performance_stats[factor_name] = {
                'total_calculations': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'total_assets': 0,
                'total_records': 0
            }
            
        stats = self._performance_stats[factor_name]
        stats['total_calculations'] += 1
        stats['total_time'] += calculation_time
        stats['avg_time'] = stats['total_time'] / stats['total_calculations']
        stats['total_assets'] += num_assets
        stats['total_records'] += num_records
        
    def get_performance_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取性能统计信息
        
        Returns:
        --------
        Dict[str, Dict[str, Any]]
            性能统计信息
        """
        return self._performance_stats.copy()
        
    def clear_cache(self):
        """清空计算缓存"""
        self._calculation_cache.clear()
        logger.info("Factor calculation cache cleared")
        
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
        --------
        Dict[str, Any]
            缓存信息
        """
        return {
            'cache_size': len(self._calculation_cache),
            'cached_factors': list(set(key.split('_')[0] for key in self._calculation_cache.keys()))
        }
        
    def _register_default_factors(self):
        """注册默认因子"""
        try:
            # 这里会在后续实现具体因子时添加注册代码
            logger.info("Default factors registration completed")
        except Exception as e:
            logger.error(f"Error registering default factors: {str(e)}")


class FactorCalculationError(Exception):
    """因子计算异常"""
    pass


class FactorDataError(Exception):
    """因子数据异常"""
    pass
