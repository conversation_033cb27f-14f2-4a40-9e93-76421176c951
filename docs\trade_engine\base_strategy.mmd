classDiagram
    class BaseStrategy {
        <<abstract>>
        -name: String
        -sizer: Sizer
        -indicator: Indicator
        -position_manager: PositionManager
        -trade_records: List
        +on_bar(data, broker, debug) Dict
        #generate_signal(data, indicators, debug) (signal, signal_info)
        +get_position(symbol) Position
        +check_risk(symbol, action, **kwargs) bool
    }

    class MyStrategy {
        +generate_signal(data, indicators, debug) (signal, signal_info)
    }

    class PositionManager {
        <<interface>>
        +get_position(symbol) Position
        +check_risk(symbol, action, **kwargs) bool
    }

    class Sizer {
        <<interface>>
        +get_size(signal, data, position, broker) int
    }

    class Indicator {
        <<interface>>
        +calculate(data) Indicators
    }

    class Broker {
        <<interface>>
        +execute_order(order) ExecutionReport
    }

    class BacktestPositionManager {
        +get_position(symbol) Position
        +check_risk(symbol, action, **kwargs) bool
    }

    class SimulationPositionManager {
        +get_position(symbol) Position
        +check_risk(symbol, action, **kwargs) bool
    }

    class FixedSizeSizer {
        +get_size(...) int
    }

    class TechnicalIndicator {
        +calculate(data) Indicators
    }

    class BacktestBroker {
        +execute_order(order) ExecutionReport
    }

    BaseStrategy <|-- MyStrategy : 继承
    BaseStrategy o-- PositionManager : 组合
    BaseStrategy o-- Sizer : 组合
    BaseStrategy o-- Indicator : 组合
    PositionManager <|.. BacktestPositionManager : 实现
    PositionManager <|.. SimulationPositionManager : 实现
    Sizer <|.. FixedSizeSizer : 实现
    Indicator <|.. TechnicalIndicator : 实现
    Broker <|.. BacktestBroker : 实现
