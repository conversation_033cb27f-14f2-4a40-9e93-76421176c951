import akshare as ak
from data_gen.data_generator_em import DataGeneratorEM
from cal_lib.cal_lib import CalLib
from analyze.market_analyze.option_market_analyze import OptionAnalyze
from plot.data_plot import DataPlot

class MarketAnalyze(DataGeneratorEM, CalLib, DataPlot):
    def __init__(self, date_range):
        super().__init__(date_range, industries=None)
        self.date_range = date_range

    def _load_data(self, option, code):
        df = self._read_csv(f"data/market/{option}/{code}.csv")
        return df

    def _market_option_discount_analysis(self, code, save=True):
        option_analyzer = OptionAnalyze()
        df = option_analyzer._option_delta_all(code, self.date_range)
        (cur_code, title) = option_analyzer._cur_code_sel(code)
        cur_df = self._load_data("current", cur_code)
        
        # Merge delta data into cur_df based on the date
        cur_df = cur_df.merge(df[['date', 'delta', 'near_close', 'far_close']], on='date', how='right')
        cur_df.to_csv(f"data/market/output/{title}.csv", index=False)
        cur_df.set_index("date", inplace=True)  # Set 'date' as the index for plotting
        keywords = ["delta"]
        
        # Plot without the x_axis argument
        if(save):
            path = f"data/market/output/{title}.png"
        else:
            path = None
            
        self.plot_line_chart(cur_df, keywords, title=f"{code}_option_analyze", out_path=path)

    def _market_option_analysis(self, year):
        self._get_option_for_year(year)
        self._get_market_hist_em()
        self._market_option_discount_analysis('IF')
        self._market_option_discount_analysis('IC')
        self._market_option_discount_analysis('IH')
        self._market_option_discount_analysis('IM')


    def _market_position_analysis(self):
        pass

    def _market_ma_analysis(self):
        pass

    def _market_vix_analysis(self):
        pass

    def _market_high_signal(self):
        pass

    def _market_low_signal(self):
        pass

