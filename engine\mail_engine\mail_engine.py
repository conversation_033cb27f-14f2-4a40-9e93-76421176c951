import requests
import json

# https://open.feishu.cn/open-apis/bot/v2/hook/be842c0a-3105-4cba-85cb-e37c58521b16

class MailAgent(object):
    def __init__(self):
        self.webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/274681d1-f6cb-47da-94df-8f8037b8ba9a"
        self.signal_sent_today = {}  # {symbol: date}

    def reset_signal_flags(self):
        self.signal_sent_today = {}

    def send_signal(self, trade_info):
        """
        Sends trade information to a webhook.
        Only sends once per symbol per day.
        """
        symbol = trade_info.get("symbol")
        date = trade_info.get("date")
        if symbol is None or date is None:
            # fallback: always send if missing key info
            pass
        else:
            if self.signal_sent_today.get(symbol) == date:
                return  # 已发送过
            self.signal_sent_today[symbol] = date

        headers = {"Content-Type": "application/json"}
        payload = {
            "msg_type": "text",
            "content": {
                "text": f"Trade Information:\n{json.dumps(trade_info, indent=2)}"
            }
        }
        try:
            response = requests.post(self.webhook_url, headers=headers, data=json.dumps(payload))
            if response.status_code != 200:
                print(f"[MailAgent] Failed to send trade info: {response.status_code}, {response.text}")
        except Exception as e:
            print(f"[MailAgent] Exception when sending trade info: {e}")
