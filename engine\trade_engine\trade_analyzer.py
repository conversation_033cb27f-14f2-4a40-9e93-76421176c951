import numpy as np

class TradeAnalyzer:
    """
    提供收益率、夏普比率、最大回撤等绩效分析方法
    输入为交易记录（list of dict）和账户快照（list of {'date', 'value'}）
    """

    @staticmethod
    def calc_returns(account_snapshots):
        """
        计算总收益率和年化收益率
        account_snapshots: [{'date': ..., 'value': ...}, ...]，按时间升序
        """
        # 过滤掉无效或重复的快照
        filtered = []
        seen = set()
        for snap in account_snapshots:
            date = snap.get("date")
            if date and date not in seen and snap.get("value", 0) > 0:
                filtered.append(snap)
                seen.add(date)
        if not filtered or len(filtered) < 2:
            return {"total_return": 0, "annual_return": 0}
        start_value = filtered[0]['value']
        end_value = filtered[-1]['value']
        total_return = (end_value - start_value) / start_value if start_value > 0 else 0
        # 年化收益率估算
        days = (filtered[-1]['date'] - filtered[0]['date']).days
        if days <= 0:
            annual_return = total_return
        else:
            annual_return = (1 + total_return) ** (365.0 / days) - 1
        return {"total_return": total_return, "annual_return": annual_return}

    @staticmethod
    def calc_sharpe(account_snapshots, risk_free_rate=0.02):
        """
        计算夏普比率
        account_snapshots: [{'date': ..., 'value': ...}, ...]，按时间升序
        risk_free_rate: 年化无风险利率
        """
        # 过滤掉无效快照
        values = [snap['value'] for snap in account_snapshots if snap.get("value", 0) > 0]
        if len(values) < 2:
            return 0
        values = np.array(values)
        rets = np.diff(values) / values[:-1]
        if len(rets) == 0:
            return 0
        mean_ret = np.mean(rets)
        std_ret = np.std(rets)
        sharpe = 0
        if std_ret > 0:
            sharpe = ((mean_ret - risk_free_rate / 252) / std_ret) * np.sqrt(252)
        return sharpe

    @staticmethod
    def calc_max_drawdown(account_snapshots):
        """
        计算最大回撤
        account_snapshots: [{'date': ..., 'value': ...}, ...]，按时间升序
        """
        values = [snap['value'] for snap in account_snapshots if snap.get("value", 0) > 0]
        if len(values) < 2:
            return 0
        values = np.array(values)
        cummax = np.maximum.accumulate(values)
        drawdowns = (values - cummax) / cummax
        max_drawdown = drawdowns.min()
        return abs(max_drawdown)

    @staticmethod
    def analyze(trade_records, account_snapshots, risk_free_rate=0.02):
        """
        综合分析，返回字典
        """
        # 过滤快照，避免全为0导致无效分析
        filtered_snapshots = [snap for snap in account_snapshots if snap.get("value", 0) > 0]
        result = {}
        result.update(TradeAnalyzer.calc_returns(filtered_snapshots))
        result["sharpe"] = TradeAnalyzer.calc_sharpe(filtered_snapshots, risk_free_rate)
        result["max_drawdown"] = TradeAnalyzer.calc_max_drawdown(filtered_snapshots)
        # 统计交易记录中的盈亏
        # 只要有profit字段的交易都计入胜率统计，包括未平仓（浮动盈亏）单
        win_count = sum(1 for rec in trade_records if rec.get("profit", 0) > 0)
        loss_count = sum(1 for rec in trade_records if rec.get("profit", 0) < 0)
        total_trades = sum(1 for rec in trade_records if "profit" in rec)
        result["total_trades"] = total_trades
        result["win_count"] = win_count
        result["loss_count"] = loss_count
        result["win_rate"] = win_count / total_trades if total_trades > 0 else 0
        result["loss_rate"] = loss_count / total_trades if total_trades > 0 else 0
        # 盈亏比
        win_profits = [rec.get("profit", 0) for rec in trade_records if rec.get("profit", 0) > 0]
        loss_profits = [abs(rec.get("profit", 0)) for rec in trade_records if rec.get("profit", 0) < 0]
        avg_win = np.mean(win_profits) if win_profits else 0
        avg_loss = np.mean(loss_profits) if loss_profits else 0
        result["profit_loss_ratio"] = (avg_win / avg_loss) if avg_loss > 0 else 0
        return result

# 说明:
# 现在所有有 profit 字段的交易（包括未平仓单的浮动盈亏）都会计入胜率统计。