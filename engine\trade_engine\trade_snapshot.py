from database.snapshot_db import SnapshotDB
import datetime

class TradeSnapshot:
    """
    提供账户快照的保存和读取接口，底层用SnapshotDB（duckdb）
    """

    def __init__(self, db_manage):
        self.db = db_manage.snapshot_db

    def save_snapshot(self, account_id, value, date=None, extra_json=None, return_rate=None):
        """
        保存一条账户快照，可选添加收益率
        :param account_id: 账户ID
        :param value: 账户总资产
        :param date: 快照日期（默认今天，字符串或datetime/date对象）
        :param extra_json: 附加信息（dict，可选）
        :param return_rate: 本次快照的账户收益率（可选）
        """
        if date is None:
            date = datetime.date.today()
        elif isinstance(date, datetime.datetime):
            date = date.date()
        if extra_json is None:
            extra_json = {}
        if return_rate is not None:
            extra_json["return_rate"] = return_rate
        self.db.insert_snapshot(account_id, str(date), value, extra_json)

    def save_snapshots_batch(self, df):
        """
        批量保存快照，df需包含account_id, date, value, extra_json列
        """
        self.db.insert_snapshots_df(df)

    def get_snapshots(self, account_id, start_date=None, end_date=None):
        """
        查询账户快照，返回DataFrame
        """
        return self.db.query_snapshots(account_id, start_date, end_date)

    def delete_snapshots(self, account_id):
        """
        删除某账户的所有快照
        """
        self.db.delete_snapshots(account_id)

    def close(self):
        self.db.close()

    def calc_today_return_rate(self, account_id, date=None):
        """
        计算指定账户当日收益率，并写入到当天快照
        :param account_id: 账户ID
        :param date: 计算哪一天的收益率（默认今天）
        :return: 当日收益率（float），若无法计算则返回None
        """
        if date is None:
            date = datetime.date.today()
        elif isinstance(date, datetime.datetime):
            date = date.date()
        # 获取最近两天快照
        df = self.get_snapshots(account_id, start_date=date - datetime.timedelta(days=1), end_date=date)
        if df is None or len(df) < 2:
            return None
        df = df.sort_values("date")
        prev_value = df.iloc[-2]["value"]
        today_row = df.iloc[-1]
        today_value = today_row["value"]
        if prev_value == 0:
            return None
        return_rate = (today_value - prev_value) / prev_value

        # 写入到当天快照（只更新return_rate字段，extra_json合并原有内容）
        extra_json = today_row.get("extra_json", {})
        if not isinstance(extra_json, dict):
            try:
                import json
                extra_json = json.loads(extra_json)
            except Exception:
                extra_json = {}
        extra_json["return_rate"] = return_rate
        self.save_snapshot(
            account_id=account_id,
            value=today_value,
            date=date,
            extra_json=extra_json,
            return_rate=return_rate
        )
        return return_rate
