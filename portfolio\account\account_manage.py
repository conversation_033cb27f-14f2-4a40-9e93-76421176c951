import os
import json
import datetime
from database.account_db import AccountDB
import pandas as pd

class AccountManage(object):
    def __init__(self):
        # Stores accounts with account_id as key
        self.default_data = {
            "name": None,
            "total_assets": 0.0,
            "created_at": None,
            "equity_pool": [],
            "trade_records": {},      # 新增：交易记录
            "performance": {          # 新增：绩效统计
                "returns": 0,
                "win_count": 0,
                "loss_count": 0,
                "total_trades": 0
            }
        }
        self.db = AccountDB()
        self._current_account_id = None  # 新增：当前选中账户ID

    def _create_account(self, account_id, account_data=None):
        """
        Create a new account with given account_id and optional account_data.
        """
        if self._load_account(account_id):
            raise ValueError(f"Account {account_id} already exists.")

        if(not account_data):
            account_data = self.default_data.copy()
            account_data["created_at"] = str(datetime.datetime.now())
            account_data["name"] = account_id

        self.db.save_account(account_id, account_data)

    @staticmethod
    def _convert(obj):
        """
        递归将所有 pandas.Timestamp 转换为字符串，便于json序列化
        """
        if isinstance(obj, dict):
            return {k: AccountManage._convert(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [AccountManage._convert(i) for i in obj]
        elif isinstance(obj, pd.Timestamp):
            return str(obj)
        else:
            return obj

    def _save_account(self, account_data):
        account_id = account_data["name"]
        if not account_id:
            raise ValueError("account_data['name'] 不能为空，保存账户失败")
        account_data_serializable = self._convert(account_data)
        self.db.save_account(account_id, account_data_serializable)

    def _load_account(self, account_id):
        """
        Load and return the account data for the given account_id.
        """
        return self.db.load_account(account_id)

    def _update_account(self, account_id, update_data):
        """
        Update the account data for the given account_id.
        """
        self.db.update_account(account_id, update_data)

    def _print_account(self, account_id):
        """
        打印账户信息（不包含trade_records）
        """
        account_data = self._load_account(account_id)
        if not account_data:
            print(f"Account {account_id} not found.")
            return
        for k, v in account_data.items():
            if k == "trade_records":
                continue
            print(f"{k}: {v}")

    def clear_trade_records(self, account_id):
        """
        清除指定账户的交易记录
        """
        account_data = self._load_account(account_id)
        if not account_data:
            raise ValueError(f"Account {account_id} not found.")
        account_data["trade_records"] = {}
        self._save_account(account_data)

    def _delete_account(self, account_id):
        """
        Delete the account with the given account_id.
        """
        self.db.delete_account(account_id)

    def _list_account(self):
        """
        List all account ids (names) from the accounts table.
        """
        return self.db.list_accounts()

    def get_accounts(self):
        """
        返回所有账户ID列表
        """
        return self._list_account()

    def set_current_account(self, account_id):
        """
        设置当前账户ID
        """
        self._current_account_id = account_id

    def get_current_account(self):
        """
        获取当前账户数据
        """
        if self._current_account_id:
            return self._load_account(self._current_account_id)
        return None



